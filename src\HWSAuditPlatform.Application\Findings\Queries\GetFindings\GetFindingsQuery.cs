using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Findings.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Findings.Queries.GetFindings;

/// <summary>
/// Query to get findings with filtering and pagination
/// </summary>
public class GetFindingsQuery : BaseQuery<PagedResult<FindingSummaryDto>>
{
    /// <summary>
    /// Filter by audit ID
    /// </summary>
    public string? AuditId { get; set; }

    /// <summary>
    /// Filter by audit template ID
    /// </summary>
    public int? AuditTemplateId { get; set; }

    /// <summary>
    /// Filter by factory ID
    /// </summary>
    public int? FactoryId { get; set; }

    /// <summary>
    /// Filter by area ID
    /// </summary>
    public int? AreaId { get; set; }

    /// <summary>
    /// Filter by finding status
    /// </summary>
    public FindingStatus? Status { get; set; }

    /// <summary>
    /// Filter by severity level
    /// </summary>
    public SeverityLevel? SeverityLevel { get; set; }

    /// <summary>
    /// Filter by finding category ID
    /// </summary>
    public int? FindingCategoryId { get; set; }

    /// <summary>
    /// Filter by responsible user ID
    /// </summary>
    public string? ResponsibleUserId { get; set; }

    /// <summary>
    /// Filter by reported by user ID
    /// </summary>
    public string? ReportedByUserId { get; set; }

    /// <summary>
    /// Filter by due date range - from
    /// </summary>
    public DateOnly? DueDateFrom { get; set; }

    /// <summary>
    /// Filter by due date range - to
    /// </summary>
    public DateOnly? DueDateTo { get; set; }

    /// <summary>
    /// Filter by creation date range - from
    /// </summary>
    public DateTime? CreatedFrom { get; set; }

    /// <summary>
    /// Filter by creation date range - to
    /// </summary>
    public DateTime? CreatedTo { get; set; }

    /// <summary>
    /// Show only overdue findings
    /// </summary>
    public bool? IsOverdue { get; set; }

    /// <summary>
    /// Show only open findings
    /// </summary>
    public bool? IsOpen { get; set; }

    /// <summary>
    /// Search term for finding description
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "CreatedAt";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}
