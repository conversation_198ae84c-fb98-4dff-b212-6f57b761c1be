using HWSAuditPlatform.Domain.Entities.Workflow;

namespace HWSAuditPlatform.Application.Interfaces;

/// <summary>
/// Service for creating and managing audit log entries
/// </summary>
public interface IAuditLogService
{
    /// <summary>
    /// Logs an entity creation event
    /// </summary>
    /// <param name="entityType">Type of entity created</param>
    /// <param name="entityId">ID of the created entity</param>
    /// <param name="newValues">JSON representation of the new entity values</param>
    /// <param name="details">Additional context or details</param>
    /// <param name="userId">User who performed the action (optional, will use current user if not provided)</param>
    /// <param name="eventTimestamp">When the event occurred (optional, will use current time if not provided)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created audit log entry</returns>
    Task<AuditLog> LogEntityCreatedAsync(
        string entityType,
        string entityId,
        string? newValues = null,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Logs an entity update event
    /// </summary>
    /// <param name="entityType">Type of entity updated</param>
    /// <param name="entityId">ID of the updated entity</param>
    /// <param name="oldValues">JSON representation of the old entity values</param>
    /// <param name="newValues">JSON representation of the new entity values</param>
    /// <param name="details">Additional context or details</param>
    /// <param name="userId">User who performed the action (optional, will use current user if not provided)</param>
    /// <param name="eventTimestamp">When the event occurred (optional, will use current time if not provided)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created audit log entry</returns>
    Task<AuditLog> LogEntityUpdatedAsync(
        string entityType,
        string entityId,
        string? oldValues = null,
        string? newValues = null,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Logs an entity deletion event
    /// </summary>
    /// <param name="entityType">Type of entity deleted</param>
    /// <param name="entityId">ID of the deleted entity</param>
    /// <param name="oldValues">JSON representation of the deleted entity values</param>
    /// <param name="details">Additional context or details</param>
    /// <param name="userId">User who performed the action (optional, will use current user if not provided)</param>
    /// <param name="eventTimestamp">When the event occurred (optional, will use current time if not provided)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created audit log entry</returns>
    Task<AuditLog> LogEntityDeletedAsync(
        string entityType,
        string entityId,
        string? oldValues = null,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Logs a business operation event
    /// </summary>
    /// <param name="actionType">Type of business action performed</param>
    /// <param name="entityType">Type of entity involved</param>
    /// <param name="entityId">ID of the entity involved</param>
    /// <param name="details">Additional context or details</param>
    /// <param name="userId">User who performed the action (optional, will use current user if not provided)</param>
    /// <param name="eventTimestamp">When the event occurred (optional, will use current time if not provided)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created audit log entry</returns>
    Task<AuditLog> LogBusinessOperationAsync(
        string actionType,
        string entityType,
        string entityId,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Logs an authentication event
    /// </summary>
    /// <param name="actionType">Type of authentication action (LoginSuccess, LoginFailure, Logout, etc.)</param>
    /// <param name="username">Username involved in the authentication event</param>
    /// <param name="details">Additional context or details</param>
    /// <param name="userId">User ID (optional, may not be available for failed logins)</param>
    /// <param name="eventTimestamp">When the event occurred (optional, will use current time if not provided)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created audit log entry</returns>
    Task<AuditLog> LogAuthenticationEventAsync(
        string actionType,
        string username,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Logs a system event
    /// </summary>
    /// <param name="actionType">Type of system action performed</param>
    /// <param name="details">Additional context or details</param>
    /// <param name="eventTimestamp">When the event occurred (optional, will use current time if not provided)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created audit log entry</returns>
    Task<AuditLog> LogSystemEventAsync(
        string actionType,
        string? details = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default);
}
