using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.CorrectiveActions.DTOs;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Application.CorrectiveActions.Commands.CreateCorrectiveAction;

/// <summary>
/// Handler for creating a new corrective action
/// </summary>
public class CreateCorrectiveActionCommandHandler : BaseCommandHandler<CreateCorrectiveActionCommand, CorrectiveActionDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateCorrectiveActionCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<CorrectiveActionDto> Handle(CreateCorrectiveActionCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Validate that the finding exists
        var finding = await _context.Findings
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
            .FirstOrDefaultAsync(f => f.Id == request.FindingId, cancellationToken);

        if (finding == null)
        {
            throw new NotFoundException("Finding", request.FindingId);
        }

        // Validate that the assigned user exists
        var assignedUser = await _context.Users
            .FirstOrDefaultAsync(u => u.AdObjectGuid == request.AssignedToUserId && u.IsActive, cancellationToken);

        if (assignedUser == null)
        {
            throw new NotFoundException("User", request.AssignedToUserId);
        }

        // Create the corrective action
        var correctiveAction = new CorrectiveAction
        {
            Id = CuidGenerator.Generate(),
            FindingId = request.FindingId,
            ActionDescription = request.ActionDescription,
            AssignedToUserId = request.AssignedToUserId,
            DueDate = request.DueDate,
            Status = CorrectiveActionStatus.Assigned
        };

        _context.CorrectiveActions.Add(correctiveAction);
        await _context.SaveChangesAsync(cancellationToken);

        // Update finding status if it's still open
        if (finding.Status == FindingStatus.Open)
        {
            finding.Status = FindingStatus.PendingCorrectiveAction;
            await _context.SaveChangesAsync(cancellationToken);
        }

        // Return the created corrective action as DTO
        return await MapToDto(correctiveAction, assignedUser, cancellationToken);
    }

    private async Task<CorrectiveActionDto> MapToDto(CorrectiveAction correctiveAction, Domain.Entities.Users.User assignedUser, CancellationToken cancellationToken)
    {
        // Note: CorrectiveAction entity doesn't have AssignedByUserId or VerifiedByUserId properties
        // These would need to be added to the domain entity if required

        return new CorrectiveActionDto
        {
            Id = correctiveAction.Id,
            FindingId = correctiveAction.FindingId,
            ActionDescription = correctiveAction.ActionDescription,
            AssignedToUserId = correctiveAction.AssignedToUserId,
            AssignedToUserName = assignedUser.Username,
            AssignedToUserFullName = assignedUser.FullName,
            AssignedByUserId = string.Empty, // Not available in current domain model
            AssignedByUserName = null,
            AssignedByUserFullName = null,
            DueDate = correctiveAction.DueDate,
            CompletionDate = correctiveAction.CompletionDate,
            VerificationDate = null, // Not available in current domain model
            VerifiedByUserId = null,
            VerifiedByUserName = null,
            VerifiedByUserFullName = null,
            Status = correctiveAction.Status,
            AssignmentNotes = null, // Not available in current domain model
            CompletionNotes = null, // Not available in current domain model
            VerificationNotes = null, // Not available in current domain model
            IsOverdue = correctiveAction.IsOverdue,
            IsCompleted = correctiveAction.IsCompleted,
            IsInProgress = correctiveAction.IsInProgress,
            CreatedAt = correctiveAction.CreatedAt,
            UpdatedAt = correctiveAction.UpdatedAt,
            CreatedBy = correctiveAction.CreatedByUserId,
            UpdatedBy = correctiveAction.UpdatedByUserId
        };
    }
}
