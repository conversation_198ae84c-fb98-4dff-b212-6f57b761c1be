using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.AuditLogs.DTOs;

namespace HWSAuditPlatform.Application.AuditLogs.Queries.GetAuditLogs;

/// <summary>
/// Query to get audit logs with filtering and pagination
/// </summary>
public class GetAuditLogsQuery : BaseQuery<PaginatedResult<AuditLogDto>>
{
    /// <summary>
    /// Filter criteria for audit logs
    /// </summary>
    public AuditLogFilterDto Filter { get; set; } = new();
}
