using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.CorrectiveActions.DTOs;

namespace HWSAuditPlatform.Application.CorrectiveActions.Commands.CreateCorrectiveAction;

/// <summary>
/// Command to create a new corrective action for a finding
/// </summary>
public class CreateCorrectiveActionCommand : BaseCommand<CorrectiveActionDto>
{
    /// <summary>
    /// The finding this corrective action addresses
    /// </summary>
    public string FindingId { get; set; } = string.Empty;

    /// <summary>
    /// Description of the corrective action to be taken
    /// </summary>
    public string ActionDescription { get; set; } = string.Empty;

    /// <summary>
    /// User ID of the person assigned to complete this action
    /// </summary>
    public string AssignedToUserId { get; set; } = string.Empty;

    /// <summary>
    /// Due date for completing this corrective action
    /// </summary>
    public DateOnly DueDate { get; set; }

    /// <summary>
    /// Optional notes about the assignment
    /// </summary>
    public string? AssignmentNotes { get; set; }
}
