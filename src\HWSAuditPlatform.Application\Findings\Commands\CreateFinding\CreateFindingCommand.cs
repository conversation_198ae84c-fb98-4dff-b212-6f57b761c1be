using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Findings.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Findings.Commands.CreateFinding;

/// <summary>
/// Command to create a new finding from an audit answer
/// </summary>
public class CreateFindingCommand : BaseCommand<FindingDto>
{
    /// <summary>
    /// The audit answer that generated this finding
    /// </summary>
    public string AuditAnswerId { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of the non-conformity
    /// </summary>
    public string FindingDescription { get; set; } = string.Empty;

    /// <summary>
    /// Severity level of the finding
    /// </summary>
    public SeverityLevel FindingSeverityLevel { get; set; } = SeverityLevel.Major;

    /// <summary>
    /// Analysis of the root cause (optional)
    /// </summary>
    public string? RootCauseAnalysis { get; set; }

    /// <summary>
    /// Description of any immediate actions taken
    /// </summary>
    public string? ImmediateActionTaken { get; set; }

    /// <summary>
    /// Target date for resolving this finding (optional)
    /// </summary>
    public DateOnly? DueDate { get; set; }

    /// <summary>
    /// Optional category for this finding
    /// </summary>
    public int? FindingCategoryId { get; set; }
}
