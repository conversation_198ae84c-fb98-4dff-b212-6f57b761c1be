using System.Text;
using System.Text.Json;
using HWSAuditPlatform.WebApp.Models;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Service for Corrective Action API operations
/// </summary>
public class CorrectiveActionApiService : ICorrectiveActionApiService
{
    private readonly AuthenticatedHttpClientService _httpClientService;
    private readonly JsonSerializerOptions _jsonOptions;

    public CorrectiveActionApiService(AuthenticatedHttpClientService httpClientService)
    {
        _httpClientService = httpClientService;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<PagedResult<CorrectiveActionDto>?> GetCorrectiveActionsAsync(
        string? searchTerm = null,
        CorrectiveActionStatus? status = null,
        string? findingId = null,
        string? assignedUserId = null,
        bool? isOverdue = null,
        DateTime? dueBefore = null,
        DateTime? dueAfter = null,
        int pageNumber = 1,
        int pageSize = 20,
        string sortBy = "CreatedAt",
        string sortDirection = "desc")
    {
        var queryParams = new List<string>();

        if (!string.IsNullOrEmpty(searchTerm))
            queryParams.Add($"searchTerm={Uri.EscapeDataString(searchTerm)}");
        if (status.HasValue)
            queryParams.Add($"status={status}");
        if (!string.IsNullOrEmpty(findingId))
            queryParams.Add($"findingId={Uri.EscapeDataString(findingId)}");
        if (!string.IsNullOrEmpty(assignedUserId))
            queryParams.Add($"assignedUserId={Uri.EscapeDataString(assignedUserId)}");
        if (isOverdue.HasValue)
            queryParams.Add($"isOverdue={isOverdue}");
        if (dueBefore.HasValue)
            queryParams.Add($"dueBefore={dueBefore:yyyy-MM-dd}");
        if (dueAfter.HasValue)
            queryParams.Add($"dueAfter={dueAfter:yyyy-MM-dd}");

        queryParams.Add($"pageNumber={pageNumber}");
        queryParams.Add($"pageSize={pageSize}");
        queryParams.Add($"sortBy={Uri.EscapeDataString(sortBy)}");
        queryParams.Add($"sortDirection={Uri.EscapeDataString(sortDirection)}");

        var queryString = string.Join("&", queryParams);
        var url = $"api/v1/corrective-actions?{queryString}";

        var response = await _httpClientService.GetAsync(url);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<PagedResult<CorrectiveActionDto>>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<CorrectiveActionDto?> GetCorrectiveActionAsync(string id)
    {
        var response = await _httpClientService.GetAsync($"api/v1/corrective-actions/{Uri.EscapeDataString(id)}");
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<CorrectiveActionDto>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<CorrectiveActionDto?> CreateCorrectiveActionAsync(CreateCorrectiveActionRequest request)
    {
        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PostAsync("api/v1/corrective-actions", content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<CorrectiveActionDto>>(responseContent, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<bool> UpdateCorrectiveActionStatusAsync(string id, CorrectiveActionStatus status, string? statusNotes = null)
    {
        var request = new UpdateCorrectiveActionStatusRequest
        {
            Status = status,
            StatusNotes = statusNotes
        };

        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PutAsync($"api/v1/corrective-actions/{Uri.EscapeDataString(id)}/status", content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, _jsonOptions);
            return apiResponse?.Success == true;
        }
        return false;
    }

    public async Task<List<CorrectiveActionDto>?> GetFindingCorrectiveActionsAsync(string findingId)
    {
        var response = await _httpClientService.GetAsync($"api/v1/corrective-actions/finding/{Uri.EscapeDataString(findingId)}");
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<CorrectiveActionDto>>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<PagedResult<CorrectiveActionDto>?> GetUserCorrectiveActionsAsync(
        string userId,
        CorrectiveActionStatus? status = null,
        int pageNumber = 1,
        int pageSize = 20)
    {
        var queryParams = new List<string>
        {
            $"pageNumber={pageNumber}",
            $"pageSize={pageSize}"
        };

        if (status.HasValue)
            queryParams.Add($"status={status}");

        var queryString = string.Join("&", queryParams);
        var url = $"api/v1/corrective-actions/user/{Uri.EscapeDataString(userId)}?{queryString}";

        var response = await _httpClientService.GetAsync(url);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<PagedResult<CorrectiveActionDto>>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<PagedResult<CorrectiveActionDto>?> GetOverdueCorrectiveActionsAsync(
        string? assignedUserId = null,
        int pageNumber = 1,
        int pageSize = 20)
    {
        var queryParams = new List<string>
        {
            $"pageNumber={pageNumber}",
            $"pageSize={pageSize}"
        };

        if (!string.IsNullOrEmpty(assignedUserId))
            queryParams.Add($"assignedUserId={Uri.EscapeDataString(assignedUserId)}");

        var queryString = string.Join("&", queryParams);
        var url = $"api/v1/corrective-actions/overdue?{queryString}";

        var response = await _httpClientService.GetAsync(url);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<PagedResult<CorrectiveActionDto>>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<CorrectiveActionStatisticsModel?> GetCorrectiveActionStatisticsAsync(
        string? assignedUserId = null,
        int? factoryId = null,
        int? areaId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        var queryParams = new List<string>();

        if (!string.IsNullOrEmpty(assignedUserId))
            queryParams.Add($"assignedUserId={Uri.EscapeDataString(assignedUserId)}");
        if (factoryId.HasValue)
            queryParams.Add($"factoryId={factoryId}");
        if (areaId.HasValue)
            queryParams.Add($"areaId={areaId}");
        if (fromDate.HasValue)
            queryParams.Add($"fromDate={fromDate:yyyy-MM-dd}");
        if (toDate.HasValue)
            queryParams.Add($"toDate={toDate:yyyy-MM-dd}");

        var queryString = queryParams.Any() ? "?" + string.Join("&", queryParams) : "";
        var url = $"api/v1/corrective-actions/statistics{queryString}";

        var response = await _httpClientService.GetAsync(url);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<CorrectiveActionStatisticsModel>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<bool> AssignCorrectiveActionAsync(string id, string assignedUserId, DateTime dueDate, string? notes = null)
    {
        var request = new
        {
            AssignedUserId = assignedUserId,
            DueDate = dueDate,
            Notes = notes
        };

        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PutAsync($"api/v1/corrective-actions/{Uri.EscapeDataString(id)}/assign", content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, _jsonOptions);
            return apiResponse?.Success == true;
        }
        return false;
    }

    public async Task<bool> CompleteCorrectiveActionAsync(string id, string completionNotes, List<string>? evidenceFileIds = null)
    {
        var request = new CompleteCorrectiveActionRequest
        {
            CompletionNotes = completionNotes,
            EvidenceFileIds = evidenceFileIds
        };

        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PutAsync($"api/v1/corrective-actions/{Uri.EscapeDataString(id)}/complete", content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, _jsonOptions);
            return apiResponse?.Success == true;
        }
        return false;
    }

    public async Task<bool> VerifyCorrectiveActionAsync(string id, bool isEffective, string verificationNotes)
    {
        var request = new VerifyCorrectiveActionRequest
        {
            IsEffective = isEffective,
            VerificationNotes = verificationNotes
        };

        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PutAsync($"api/v1/corrective-actions/{Uri.EscapeDataString(id)}/verify", content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, _jsonOptions);
            return apiResponse?.Success == true;
        }
        return false;
    }
}
