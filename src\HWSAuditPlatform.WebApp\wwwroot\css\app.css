/* Industrial Theme - HWS Audit Platform */

html, body {
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    color: #e0e0e0;
    margin: 0;
    padding: 0;
}

/* Industrial Color Palette */
:root {
    --industrial-dark: #1a1a1a;
    --industrial-darker: #0f0f0f;
    --industrial-gray: #2d2d2d;
    --industrial-light-gray: #404040;
    --industrial-steel: #4a5568;
    --industrial-blue: #2b6cb0;
    --industrial-orange: #ed8936;
    --industrial-teal: rgb(10, 231, 213);
    --industrial-red: #e53e3e;
    --industrial-green: #38a169;
    --industrial-text: #e0e0e0;
    --industrial-text-muted: #a0a0a0;
    --industrial-border: #404040;
}

a, .btn-link {
    color: var(--industrial-orange);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover, .btn-link:hover {
    color: #fbb040;
    text-decoration: none;
}

.btn-primary {
    color: #fff;
    background: linear-gradient(135deg, var(--industrial-orange) 0%, #d69e2e 100%);
    border: 1px solid var(--industrial-orange);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #fbb040 0%, var(--industrial-orange) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
    box-shadow: 0 0 0 0.1rem var(--industrial-dark), 0 0 0 0.25rem var(--industrial-orange);
}

.content {
    padding-top: 1.1rem;
    background-color: var(--industrial-dark);
    min-height: calc(100vh - 3.5rem);
}

/* Industrial Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Roboto Condensed', sans-serif;
    color: var(--industrial-text);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

h1:focus {
    outline: none;
}

/* Industrial Form Styling */
.form-control {
    background-color: var(--industrial-gray);
    border: 1px solid var(--industrial-border);
    color: var(--industrial-text);
    border-radius: 2px;
}

.form-control:focus {
    background-color: var(--industrial-light-gray);
    border-color: var(--industrial-orange);
    color: var(--industrial-text);
    box-shadow: 0 0 0 0.2rem rgba(237, 137, 54, 0.25);
}

.form-control::placeholder {
    color: var(--industrial-text-muted);
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid var(--industrial-green);
    border-color: var(--industrial-green);
}

.invalid {
    outline: 1px solid var(--industrial-red);
    border-color: var(--industrial-red);
}

.validation-message {
    color: var(--industrial-red);
    font-weight: 500;
}

#blazor-error-ui {
    color-scheme: light only;
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI6My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

/* Industrial Cards and Panels */
.card {
    background-color: var(--industrial-gray);
    border: 1px solid var(--industrial-border);
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.card-header {
    background: linear-gradient(135deg, var(--industrial-steel) 0%, var(--industrial-light-gray) 100%);
    border-bottom: 2px solid var(--industrial-orange);
    color: var(--industrial-text);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-body {
    color: var(--industrial-text);
}

/* Industrial Tables */
.table {
    color: var(--industrial-text) !important;
    background-color: var(--industrial-gray) !important;
    border-color: var(--industrial-border) !important;
}

.table th {
    background-color: var(--industrial-steel) !important;
    color: var(--industrial-text) !important;
    border-color: var(--industrial-border) !important;
    font-weight: 700 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-top: none !important;
}

.table td {
    border-color: var(--industrial-border) !important;
    color: var(--industrial-text) !important;
    background-color: transparent !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(64, 64, 64, 0.3) !important;
}

.table-hover > tbody > tr:hover > td {
    background-color: rgba(237, 137, 54, 0.1) !important;
    color: var(--industrial-text) !important;
}

.table-hover > tbody > tr:hover {
    color: var(--industrial-text) !important;
}

/* Industrial Table Warning Rows */
.table-warning {
    background-color: rgba(237, 137, 54, 0.2) !important;
    color: var(--industrial-text) !important;
}

.table-warning > td {
    background-color: rgba(237, 137, 54, 0.2) !important;
    color: var(--industrial-text) !important;
}

/* Table cell badge handling */
.table td .badge-container {
    max-width: 100%;
    justify-content: flex-start;
}

.table td .badge {
    max-width: 120px; /* Constrain badges in table cells */
}

/* Timeline and card badge improvements */
.timeline-content .badge-container,
.card-body .badge-container {
    max-width: 100%;
}

.timeline-content .badge,
.card-body .badge {
    max-width: 150px;
}

/* Industrial Badges */
.badge {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    /* Prevent badge overflow */
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
    /* Responsive font sizing */
    font-size: 0.75rem;
    line-height: 1.2;
    padding: 0.375rem 0.75rem;
}

/* Badge size variants for better control */
.badge-sm {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
    max-width: 120px;
}

.badge-lg {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    max-width: 200px;
}

/* Badge container helpers */
.badge-container {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-wrap: wrap;
    min-width: 0; /* Allow flex items to shrink */
}

.badge-container .badge {
    flex-shrink: 1;
    min-width: 0;
}

/* Responsive badge adjustments */
@media (max-width: 768px) {
    .badge {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        max-width: 100px;
    }

    .badge-lg {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        max-width: 150px;
    }
}

@media (max-width: 576px) {
    .badge {
        font-size: 0.6rem;
        padding: 0.2rem 0.4rem;
        max-width: 80px;
    }

    .badge-lg {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        max-width: 120px;
    }

    /* Extra small badges for mobile */
    .badge-xs {
        font-size: 0.55rem;
        padding: 0.15rem 0.3rem;
        max-width: 60px;
    }
}

/* Special handling for very long text badges */
.badge-truncate {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
}

.badge-wrap {
    white-space: normal;
    word-break: break-word;
    line-height: 1.3;
    max-width: 200px;
}

/* Badge tooltip support */
.badge[title] {
    cursor: help;
}

.bg-primary {
    background: linear-gradient(135deg, var(--industrial-orange) 0%, #d69e2e 100%) !important;
}

/* Industrial Utility Classes */
.text-muted {
    color: #c0c0c0 !important;
    font-weight: 500;
}

.border {
    border-color: var(--industrial-border) !important;
}

/* Improve text readability on dark backgrounds */
small.text-muted {
    color: #d0d0d0 !important;
}

.text-danger {
    color: #ff6b6b !important;
    font-weight: 600;
}

.text-success {
    color: #4ecdc4 !important;
    font-weight: 600;
}

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

    .loading-progress circle {
        fill: none;
        stroke: #e0e0e0;
        stroke-width: 0.6rem;
        transform-origin: 50% 50%;
        transform: rotate(-90deg);
    }

        .loading-progress circle:last-child {
            stroke: #1b6ec2;
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

    .loading-progress-text:after {
        content: var(--blazor-load-percentage-text, "Loading");
    }

code {
    color: #c02d76;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
    color: var(--bs-secondary-color);
    text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
    text-align: start;
}

/* User Search Dropdown Styling */
.user-search-dropdown {
    background-color: var(--industrial-gray);
    border: 1px solid var(--industrial-orange);
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
    margin-top: 2px;
}

.user-search-dropdown .dropdown-item {
    background-color: transparent;
    color: var(--industrial-text);
    border: none;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--industrial-border);
}

.user-search-dropdown .dropdown-item:last-child {
    border-bottom: none;
}

.user-search-dropdown .dropdown-item:hover,
.user-search-dropdown .dropdown-item:focus {
    background-color: var(--industrial-orange);
    color: #fff;
    transform: translateX(2px);
}

.user-search-dropdown .dropdown-item .fw-medium {
    font-weight: 600;
    font-size: 0.95rem;
}

.user-search-dropdown .dropdown-item small {
    font-size: 0.8rem;
    opacity: 0.8;
}

.user-search-dropdown .dropdown-item:hover small,
.user-search-dropdown .dropdown-item:focus small {
    opacity: 1;
}

.user-search-dropdown .dropdown-item-text {
    color: var(--industrial-text-muted);
    font-style: italic;
    padding: 0.75rem 1rem;
}