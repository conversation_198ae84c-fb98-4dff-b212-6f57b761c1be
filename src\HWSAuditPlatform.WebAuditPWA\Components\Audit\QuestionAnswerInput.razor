@using HWSAuditPlatform.Domain.Entities.Templates
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebAuditPWA.Models

<div class="answer-input-container">
    @switch (Question?.QuestionType)
    {
        case QuestionType.YesNo:
            @* Previous answer in separate row above buttons *@
            @if (ShowPreviousAnswer && Answer != null)
            {
                <div class="previous-answer-indicator">
                    <i class="fas fa-history"></i>
                    <span>Previous answer: <strong>@(Answer.AnswerBoolean == true ? "Yes" : Answer.AnswerBoolean == false ? "No" : "Not answered")</strong></span>
                    @if (Answer.AnswerBoolean == false && Answer.HasFailureReasons())
                    {
                        <div class="previous-findings">
                            <small>Previous findings: @string.Join("; ", Answer.GetFailureReasons())</small>
                        </div>
                    }
                </div>
            }

            @* Yes/No buttons in their own container *@
            <div class="yesno-container">
                <div class="yesno-option">
                    <input type="radio"
                           class="yesno-radio"
                           name="<EMAIL>"
                           id="<EMAIL>"
                           checked="@(Answer?.AnswerBoolean == true)"
                           @onchange="@(() => HandleBooleanAnswer(true))" />
                    <label class="yesno-label yesno-yes" for="<EMAIL>">
                        <div class="yesno-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <span class="yesno-text">Yes</span>
                    </label>
                </div>

                <div class="yesno-option">
                    <input type="radio"
                           class="yesno-radio"
                           name="<EMAIL>"
                           id="<EMAIL>"
                           checked="@(Answer?.AnswerBoolean == false)"
                           @onchange="@(() => HandleBooleanAnswer(false))" />
                    <label class="yesno-label yesno-no" for="<EMAIL>">
                        <div class="yesno-icon">
                            <i class="fas fa-times"></i>
                        </div>
                        <span class="yesno-text">No</span>
                    </label>
                </div>
            </div>

            @* Show findings input when "No" is selected *@
            @if (Answer?.AnswerBoolean == false)
            {
                <EnhancedFindingsInput Findings="currentFindings"
                                     FindingsChanged="HandleFindingsChanged"
                                     DetailedFindingsChanged="HandleDetailedFindingsChanged"
                                     ShowValidationSummary="showFindingsValidation"
                                     EnableCategorization="enableCategorization"
                                     AuditTemplateId="auditTemplateId"
                                     SelectedCategoryId="selectedCategoryId"
                                     SelectedCategoryIdChanged="HandleCategoryChanged"
                                     ShowResponsibilityInfo="showResponsibilityInfo"
                                     ResponsibleUserName="responsibleUserName"
                                     RetrospectiveAnalystUserName="retrospectiveAnalystUserName" />
            }
            break;

        case QuestionType.ShortText:
        case QuestionType.LongText:
            @if (ShowPreviousAnswer && Answer != null && !string.IsNullOrWhiteSpace(Answer.AnswerText))
            {
                <div class="previous-answer-indicator">
                    <i class="fas fa-history"></i>
                    <span>Previous answer: <strong>@Answer.AnswerText</strong></span>
                </div>
            }
            <div class="text-input-container">
                <textarea class="form-control text-input"
                         rows="@(Question.QuestionType == QuestionType.LongText ? 6 : 3)"
                         placeholder="Enter your answer..."
                         value="@(Answer?.AnswerText ?? "")"
                         @onchange="@((e) => HandleTextAnswer(e.Value?.ToString() ?? ""))">
                </textarea>
            </div>
            break;

        case QuestionType.Numeric:
            @if (ShowPreviousAnswer && Answer != null && Answer.AnswerNumeric.HasValue)
            {
                <div class="previous-answer-indicator">
                    <i class="fas fa-history"></i>
                    <span>Previous answer: <strong>@Answer.AnswerNumeric.Value</strong></span>
                </div>
            }
            <div class="numeric-input-container">
                <input type="number"
                       class="form-control numeric-input"
                       placeholder="Enter a number..."
                       value="@(Answer?.AnswerNumeric?.ToString() ?? "")"
                       @onchange="@((e) => HandleNumericAnswer(e.Value?.ToString()))" />
            </div>
            break;

        case QuestionType.Date:
            @if (ShowPreviousAnswer && Answer != null && Answer.AnswerDate.HasValue)
            {
                <div class="previous-answer-indicator">
                    <i class="fas fa-history"></i>
                    <span>Previous answer: <strong>@Answer.AnswerDate.Value.ToString("yyyy-MM-dd")</strong></span>
                </div>
            }
            <div class="date-input-container">
                <input type="date"
                       class="form-control date-input"
                       value="@(Answer?.AnswerDate?.ToString("yyyy-MM-dd") ?? "")"
                       @onchange="@((e) => HandleDateAnswer(e.Value?.ToString()))" />
            </div>
            break;

        case QuestionType.SingleSelect:
            @if (Question?.Options?.Any() == true)
            {
                <div class="single-select-container">
                    @foreach (var option in Question.Options)
                    {
                        <div class="select-option-item">
                            <input type="radio" 
                                   class="select-radio" 
                                   name="<EMAIL>" 
                                   id="<EMAIL><EMAIL>"
                                   checked="@(Answer?.SelectedOptionId == option.Id)"
                                   @onchange="@(() => HandleSingleSelectAnswer(option.Id))" />
                            <label class="select-label" for="<EMAIL><EMAIL>">
                                <div class="radio-indicator">
                                    <div class="radio-dot"></div>
                                </div>
                                <span class="option-text">@option.OptionText</span>
                            </label>
                        </div>
                    }
                </div>
            }
            break;

        case QuestionType.MultiSelect:
            @if (Question?.Options?.Any() == true)
            {
                <div class="multi-select-container">
                    @foreach (var option in Question.Options)
                    {
                        <div class="select-option-item">
                            <input type="checkbox" 
                                   class="select-checkbox" 
                                   id="<EMAIL><EMAIL>"
                                   checked="@(Answer?.GetSelectedOptionIds()?.Contains(option.Id) == true)"
                                   @onchange="@((e) => HandleMultiSelectAnswer(option.Id, (bool)(e.Value ?? false)))" />
                            <label class="select-label" for="<EMAIL><EMAIL>">
                                <div class="checkbox-indicator">
                                    <i class="fas fa-check checkbox-check"></i>
                                </div>
                                <span class="option-text">@option.OptionText</span>
                            </label>
                        </div>
                    }
                </div>
            }
            break;
    }
</div>

@code {
    [Parameter] public Question? Question { get; set; }
    [Parameter] public AuditAnswer? Answer { get; set; }
    [Parameter] public EventCallback<AuditAnswer> OnAnswerChanged { get; set; }
    [Parameter] public bool ShowPreviousAnswer { get; set; } = false;
    [Parameter] public EventCallback<bool> OnValidationStateChanged { get; set; }

    // New parameters for enhanced findings
    [Parameter] public int? AuditTemplateId { get; set; }
    [Parameter] public bool EnableCategorization { get; set; } = false;
    [Parameter] public bool ShowResponsibilityInfo { get; set; } = false;
    [Parameter] public string? ResponsibleUserName { get; set; }
    [Parameter] public string? RetrospectiveAnalystUserName { get; set; }

    private List<string> currentFindings = new();
    private bool showFindingsValidation = false;
    private int? selectedCategoryId = null;

    // Computed properties for enhanced findings
    private bool enableCategorization => EnableCategorization && AuditTemplateId.HasValue;
    private int? auditTemplateId => AuditTemplateId;
    private bool showResponsibilityInfo => ShowResponsibilityInfo && (HasResponsibleUser || HasRetrospectiveAnalyst);
    private string? responsibleUserName => ResponsibleUserName;
    private string? retrospectiveAnalystUserName => RetrospectiveAnalystUserName;

    private bool HasResponsibleUser => !string.IsNullOrEmpty(ResponsibleUserName);
    private bool HasRetrospectiveAnalyst => !string.IsNullOrEmpty(RetrospectiveAnalystUserName);

    protected override void OnParametersSet()
    {
        // Initialize findings from existing answer only if we don't already have findings
        // This prevents overwriting findings that were just updated via HandleFindingsChanged
        if (Answer != null && !currentFindings.Any())
        {
            currentFindings = Answer.GetFailureReasons();
        }
        else if (Answer == null && !currentFindings.Any())
        {
            currentFindings = new List<string>();
        }
    }

    private async Task HandleBooleanAnswer(bool value)
    {
        var updatedAnswer = CreateOrUpdateAnswer();
        updatedAnswer.AnswerBoolean = value;

        // Clear findings if changing from "No" to "Yes"
        if (value == true)
        {
            currentFindings.Clear();
            updatedAnswer.SetFailureReasons(currentFindings);
            showFindingsValidation = false;
        }
        else if (value == false)
        {
            // Initialize findings if not already present
            if (!currentFindings.Any())
            {
                currentFindings = new List<string> { "" };
            }
            updatedAnswer.SetFailureReasons(currentFindings);
        }

        ClearOtherAnswerTypes(updatedAnswer, "boolean");
        await NotifyAnswerChanged(updatedAnswer);
        await NotifyValidationStateChanged();
    }

    private async Task HandleTextAnswer(string value)
    {
        var updatedAnswer = CreateOrUpdateAnswer();
        updatedAnswer.AnswerText = string.IsNullOrWhiteSpace(value) ? null : value;
        ClearOtherAnswerTypes(updatedAnswer, "text");
        await NotifyAnswerChanged(updatedAnswer);
    }

    private async Task HandleNumericAnswer(string? value)
    {
        var updatedAnswer = CreateOrUpdateAnswer();
        if (decimal.TryParse(value, out var numericValue))
        {
            updatedAnswer.AnswerNumeric = numericValue;
        }
        else
        {
            updatedAnswer.AnswerNumeric = null;
        }
        ClearOtherAnswerTypes(updatedAnswer, "numeric");
        await NotifyAnswerChanged(updatedAnswer);
    }

    private async Task HandleDateAnswer(string? value)
    {
        var updatedAnswer = CreateOrUpdateAnswer();
        if (DateTime.TryParse(value, out var dateValue))
        {
            updatedAnswer.AnswerDate = dateValue;
        }
        else
        {
            updatedAnswer.AnswerDate = null;
        }
        ClearOtherAnswerTypes(updatedAnswer, "date");
        await NotifyAnswerChanged(updatedAnswer);
    }

    private async Task HandleSingleSelectAnswer(int optionId)
    {
        var updatedAnswer = CreateOrUpdateAnswer();
        updatedAnswer.SelectedOptionId = optionId;
        ClearOtherAnswerTypes(updatedAnswer, "single");
        await NotifyAnswerChanged(updatedAnswer);
    }

    private async Task HandleMultiSelectAnswer(int optionId, bool isSelected)
    {
        var updatedAnswer = CreateOrUpdateAnswer();

        // Handle multi-select logic using the extension method
        var currentSelectedIds = updatedAnswer.GetSelectedOptionIds();

        if (isSelected)
        {
            if (!currentSelectedIds.Contains(optionId))
            {
                currentSelectedIds.Add(optionId);
            }
        }
        else
        {
            currentSelectedIds.Remove(optionId);
        }

        // Set the updated selection
        updatedAnswer.SetSelectedOptionIds(currentSelectedIds);

        ClearOtherAnswerTypes(updatedAnswer, "multi");
        await NotifyAnswerChanged(updatedAnswer);
    }

    private AuditAnswer CreateOrUpdateAnswer()
    {
        if (Answer != null)
        {
            Answer.UpdatedAt = DateTime.UtcNow;
            return Answer;
        }

        if (Question == null)
            throw new InvalidOperationException("Question is required to create an answer");

        return new AuditAnswer
        {
            Id = Guid.NewGuid().ToString(),
            QuestionId = Question.Id,
            Question = Question,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    private void ClearOtherAnswerTypes(AuditAnswer answer, string keepType)
    {
        if (keepType != "boolean") answer.AnswerBoolean = null;
        if (keepType != "text") answer.AnswerText = null;
        if (keepType != "numeric") answer.AnswerNumeric = null;
        if (keepType != "date") answer.AnswerDate = null;
        if (keepType != "single" && keepType != "multi") answer.SelectedOptionId = null;
    }

    private async Task NotifyAnswerChanged(AuditAnswer answer)
    {
        if (OnAnswerChanged.HasDelegate)
        {
            await OnAnswerChanged.InvokeAsync(answer);
        }
    }

    private async Task HandleFindingsChanged(List<string> findings)
    {
        Console.WriteLine($"HandleFindingsChanged called with {findings?.Count ?? 0} findings");
        Console.WriteLine($"Findings content: {string.Join(", ", findings?.Select((f, i) => $"[{i}]: '{f}'") ?? Enumerable.Empty<string>())}");

        // Create a new list to ensure proper parameter binding
        currentFindings = new List<string>(findings ?? new List<string>());
        Console.WriteLine($"Updated currentFindings count: {currentFindings.Count}");

        if (Answer != null)
        {
            Answer.SetFailureReasons(findings ?? new List<string>());
            await NotifyAnswerChanged(Answer);
        }

        await NotifyValidationStateChanged();
        StateHasChanged(); // Force re-render to update the child component
        Console.WriteLine("HandleFindingsChanged completed");
    }

    private async Task HandleDetailedFindingsChanged(List<DetailedFindingModel> detailedFindings)
    {
        Console.WriteLine($"HandleDetailedFindingsChanged called with {detailedFindings?.Count ?? 0} detailed findings");

        if (Answer != null)
        {
            // Store detailed findings information in the answer
            Answer.SetDetailedFindings(detailedFindings ?? new List<DetailedFindingModel>());
            await NotifyAnswerChanged(Answer);
        }

        await NotifyValidationStateChanged();
        StateHasChanged();
        Console.WriteLine("HandleDetailedFindingsChanged completed");
    }

    private async Task NotifyValidationStateChanged()
    {
        bool isValid = IsAnswerValid();

        if (OnValidationStateChanged.HasDelegate)
        {
            await OnValidationStateChanged.InvokeAsync(isValid);
        }
    }

    private async Task HandleCategoryChanged(int? categoryId)
    {
        selectedCategoryId = categoryId;
        // TODO: Store category assignment for the finding
        // This would typically be handled when the audit is submitted
        // or when findings are created/updated
        StateHasChanged();
    }

    public bool IsAnswerValid()
    {
        // For Yes/No questions with "No" answer, require at least one finding
        if (Question?.QuestionType == QuestionType.YesNo && Answer?.AnswerBoolean == false)
        {
            return currentFindings?.Any(f => !string.IsNullOrWhiteSpace(f)) == true;
        }

        // For other question types, use existing validation logic
        return Answer != null && HasAnswer(Answer);
    }

    public void ShowValidationErrors()
    {
        if (Question?.QuestionType == QuestionType.YesNo && Answer?.AnswerBoolean == false)
        {
            showFindingsValidation = true;
            StateHasChanged();
        }
    }

    private bool HasAnswer(AuditAnswer answer)
    {
        return answer.AnswerBoolean.HasValue ||
               !string.IsNullOrWhiteSpace(answer.AnswerText) ||
               answer.AnswerNumeric.HasValue ||
               answer.AnswerDate.HasValue ||
               answer.SelectedOptionId.HasValue ||
               (answer.SelectedOptions?.Any() == true);
    }
}

<style>
    .answer-input-container {
        margin: 1.5rem 0;
    }

    /* Previous Answer Indicator */
    .previous-answer-indicator {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        background: linear-gradient(135deg, rgba(20, 184, 166, 0.1) 0%, rgba(20, 184, 166, 0.05) 100%);
        border: 1px solid rgba(20, 184, 166, 0.3);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        color: var(--industrial-text);
        font-size: 0.9rem;
    }

    .previous-answer-indicator i {
        color: var(--industrial-teal);
        font-size: 1rem;
        margin-top: 0.1rem;
        flex-shrink: 0;
    }

    .previous-answer-indicator strong {
        color: var(--industrial-teal);
        font-weight: 600;
    }

    .previous-findings {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid rgba(20, 184, 166, 0.2);
        font-size: 0.85rem;
        color: var(--industrial-text-muted);
        line-height: 1.4;
    }

    /* Yes/No Buttons */
    .yesno-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin: 1rem 0;
    }

    .yesno-option {
        position: relative;
    }

    .yesno-radio {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .yesno-label {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 1.5rem;
        border: 2px solid var(--industrial-border);
        border-radius: 12px;
        background: linear-gradient(135deg, var(--industrial-gray) 0%, #252525 100%);
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 100px;
        gap: 0.75rem;
        text-align: center;
    }

    .yesno-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        transition: all 0.3s ease;
    }

    .yesno-text {
        font-weight: 700;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        color: var(--industrial-text);
    }

    .yesno-yes .yesno-icon {
        background: var(--industrial-green);
        color: white;
    }

    .yesno-no .yesno-icon {
        background: var(--industrial-red);
        color: white;
    }

    .yesno-radio:checked + .yesno-label {
        border-color: var(--industrial-teal);
        background: linear-gradient(135deg, var(--industrial-teal) 0%, var(--industrial-teal-dark) 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 24px var(--industrial-glow);
    }

    .yesno-radio:checked + .yesno-label .yesno-text {
        color: white;
    }

    .yesno-radio:checked + .yesno-label .yesno-icon {
        background: white;
        color: var(--industrial-teal);
        transform: scale(1.1);
    }

    /* Form Controls */
    .text-input,
    .numeric-input,
    .date-input {
        background-color: var(--industrial-gray);
        border: 2px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 8px;
        padding: 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        resize: vertical;
    }

    .text-input:focus,
    .numeric-input:focus,
    .date-input:focus {
        background-color: var(--industrial-light-gray);
        border-color: var(--industrial-teal);
        color: var(--industrial-text);
        box-shadow: 0 0 0 0.2rem rgba(20, 184, 166, 0.25);
        outline: none;
    }

    .text-input::placeholder,
    .numeric-input::placeholder {
        color: var(--industrial-text-muted);
    }

    /* Select Options */
    .single-select-container,
    .multi-select-container {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        margin: 1rem 0;
    }

    .select-option-item {
        position: relative;
    }

    .select-radio,
    .select-checkbox {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .select-label {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 1.25rem;
        border: 2px solid var(--industrial-border);
        border-radius: 8px;
        background: linear-gradient(135deg, var(--industrial-gray) 0%, #252525 100%);
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 60px;
    }

    .select-label:hover {
        border-color: var(--industrial-teal);
        background: linear-gradient(135deg, var(--industrial-light-gray) 0%, var(--industrial-gray) 100%);
    }

    .radio-indicator,
    .checkbox-indicator {
        width: 24px;
        height: 24px;
        border: 2px solid var(--industrial-border-light);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .radio-indicator {
        border-radius: 50%;
    }

    .checkbox-indicator {
        border-radius: 4px;
    }

    .radio-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--industrial-teal);
        transform: scale(0);
        transition: transform 0.3s ease;
    }

    .checkbox-check {
        color: var(--industrial-teal);
        font-size: 0.9rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .option-text {
        color: var(--industrial-text);
        font-weight: 500;
        line-height: 1.4;
        flex: 1;
    }

    .select-radio:checked + .select-label {
        border-color: var(--industrial-teal);
        background: linear-gradient(135deg, rgba(20, 184, 166, 0.1) 0%, rgba(20, 184, 166, 0.05) 100%);
    }

    .select-radio:checked + .select-label .radio-indicator {
        border-color: var(--industrial-teal);
        background: var(--industrial-teal);
    }

    .select-radio:checked + .select-label .radio-dot {
        transform: scale(1);
        background: white;
    }

    .select-radio:checked + .select-label .option-text {
        color: var(--industrial-teal);
        font-weight: 600;
    }

    .select-checkbox:checked + .select-label {
        border-color: var(--industrial-teal);
        background: linear-gradient(135deg, rgba(20, 184, 166, 0.1) 0%, rgba(20, 184, 166, 0.05) 100%);
    }

    .select-checkbox:checked + .select-label .checkbox-indicator {
        border-color: var(--industrial-teal);
        background: var(--industrial-teal);
    }

    .select-checkbox:checked + .select-label .checkbox-check {
        opacity: 1;
        color: white;
    }

    .select-checkbox:checked + .select-label .option-text {
        color: var(--industrial-teal);
        font-weight: 600;
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .previous-answer-indicator {
            flex-direction: column;
            gap: 0.5rem;
            padding: 0.875rem;
            font-size: 0.85rem;
        }

        .previous-answer-indicator i {
            align-self: flex-start;
        }

        .previous-findings {
            margin-top: 0.375rem;
            padding-top: 0.375rem;
            font-size: 0.8rem;
        }

        .yesno-container {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .yesno-label {
            flex-direction: row;
            padding: 1.25rem;
            min-height: 80px;
            text-align: left;
        }

        .yesno-icon {
            width: 40px;
            height: 40px;
            font-size: 1.25rem;
            margin-right: 1rem;
        }

        .yesno-text {
            font-size: 1rem;
        }

        .select-label {
            padding: 0.875rem 1rem;
            min-height: 56px;
        }

        .option-text {
            font-size: 0.95rem;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .yesno-label {
            min-height: 88px;
            padding: 1.5rem;
        }

        .select-label {
            min-height: 64px;
            padding: 1.25rem;
        }

        .text-input,
        .numeric-input,
        .date-input {
            padding: 1.25rem;
            font-size: 16px; /* Prevents zoom on iOS */
            min-height: 56px;
        }
    }
</style>
