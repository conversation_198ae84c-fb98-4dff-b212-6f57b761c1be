using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Findings.DTOs;

namespace HWSAuditPlatform.Application.Findings.Queries.GetFinding;

/// <summary>
/// Query to get a single finding by ID
/// </summary>
public class GetFindingQuery : BaseQuery<FindingDto>
{
    /// <summary>
    /// The ID of the finding to retrieve
    /// </summary>
    public string FindingId { get; set; } = string.Empty;

    /// <summary>
    /// Whether to include corrective actions in the response
    /// </summary>
    public bool IncludeCorrectiveActions { get; set; } = true;
}
