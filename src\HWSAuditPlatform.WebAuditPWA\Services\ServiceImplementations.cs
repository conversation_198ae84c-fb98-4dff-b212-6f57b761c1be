using System.Text.Json;
using System.Net.Http.Json;
using Blazored.LocalStorage;
using Microsoft.JSInterop;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.WebAuditPWA.Models;
using Microsoft.Extensions.Logging;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Templates.DTOs;

namespace HWSAuditPlatform.WebAuditPWA.Services;

/// <summary>
/// Implementation of audit API service
/// </summary>
public class AuditApiService : IAuditApiService
{
    private readonly AuthenticatedHttpClientService _httpClient;
    private readonly ILogger<AuditApiService> _logger;

    public AuditApiService(AuthenticatedHttpClientService httpClient, ILogger<AuditApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<Result<List<Audit>>> GetAssignedAuditsAsync()
    {
        try
        {
            _logger.LogInformation("Getting assigned audits for current user");

            var response = await _httpClient.GetAsync("api/v1/audits/my-audits");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<PagedResult<Application.Audits.DTOs.AuditSummaryDto>>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    // Convert DTOs to domain entities
                    var audits = apiResponse.Data.Items.Select(dto => new Audit
                    {
                        Id = dto.Id,
                        OverallStatus = dto.OverallStatus,
                        ScheduledDate = dto.ScheduledDate,
                        DueDate = dto.DueDate,
                        OverallScore = dto.OverallScore,
                        AuditTemplate = new AuditTemplate { TemplateName = dto.AuditTemplateName ?? "" },
                        Factory = new Factory { FactoryName = dto.FactoryName ?? "" },
                        Area = new Area { AreaName = dto.AreaName ?? "" }
                        // Note: Application layer's AuditSummaryDto doesn't have StartedAt, CompletedAt, or SubAreaName
                    }).ToList();

                    _logger.LogInformation("Retrieved {Count} assigned audits", audits.Count);
                    return Result<List<Audit>>.Success(audits);
                }
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to get assigned audits. Status: {StatusCode}, Content: {Content}",
                response.StatusCode, errorContent);

            return Result<List<Audit>>.Failure("Failed to retrieve assigned audits");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting assigned audits");
            return Result<List<Audit>>.Failure("An error occurred while retrieving audits");
        }
    }

    public async Task<Result<Audit?>> GetAuditAsync(string auditId)
    {
        try
        {
            _logger.LogInformation("Getting audit details for ID: {AuditId}", auditId);

            var response = await _httpClient.GetAsync($"api/v1/audits/{auditId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AuditDto>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Retrieved audit details for ID: {AuditId}", auditId);
                    var audit = MapAuditDtoToEntity(apiResponse.Data);
                    return Result<Audit?>.Success(audit);
                }
            }

            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Audit not found: {AuditId}", auditId);
                return Result<Audit?>.Success(null);
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to get audit. Status: {StatusCode}, Content: {Content}",
                response.StatusCode, errorContent);

            return Result<Audit?>.Failure("Failed to retrieve audit details");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit: {AuditId}", auditId);
            return Result<Audit?>.Failure("An error occurred while retrieving audit details");
        }
    }

    public async Task<Result<Models.AuditSummaryDto?>> GetAuditForResultsAsync(string auditId)
    {
        try
        {
            _logger.LogInformation("Getting detailed audit results for ID: {AuditId}", auditId);

            var response = await _httpClient.GetAsync($"api/v1/audits/{auditId}/review");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<Application.Audits.DTOs.AuditReviewDto>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Retrieved detailed audit results for ID: {AuditId}", auditId);
                    var auditSummary = MapAuditReviewDtoToSummary(apiResponse.Data);
                    return Result<Models.AuditSummaryDto?>.Success(auditSummary);
                }
            }

            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Audit not found for results: {AuditId}", auditId);
                return Result<Models.AuditSummaryDto?>.Success(null);
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to get audit results. Status: {StatusCode}, Content: {Content}",
                response.StatusCode, errorContent);

            return Result<Models.AuditSummaryDto?>.Failure("Failed to retrieve detailed audit results");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit results: {AuditId}", auditId);
            return Result<Models.AuditSummaryDto?>.Failure("An error occurred while retrieving detailed audit results");
        }
    }

    public async Task<Result> StartAuditAsync(string auditId)
    {
        try
        {
            _logger.LogInformation("Starting audit: {AuditId}", auditId);

            var response = await _httpClient.PostAsync($"api/v1/audits/{auditId}/start", null);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully started audit: {AuditId}", auditId);
                return Result.Success();
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to start audit. Status: {StatusCode}, Content: {Content}",
                response.StatusCode, errorContent);

            return Result.Failure("Failed to start audit");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting audit: {AuditId}", auditId);
            return Result.Failure("An error occurred while starting the audit");
        }
    }

    public async Task<Result> SubmitAuditAnswersAsync(string auditId, List<AuditAnswer> answers)
    {
        try
        {
            _logger.LogInformation("Submitting {Count} answers for audit: {AuditId}", answers.Count, auditId);

            foreach (var answer in answers)
            {
                // Get the answer value, selected options, and failure reasons
                var answerValue = GetAnswerValueString(answer);
                var selectedOptionIds = answer.GetSelectedOptionIds()?.ToList() ?? new List<int>();
                var failureReasonTexts = answer.GetFailureReasons();
                var enhancedFindings = answer.GetEnhancedFindingData();

                // For multi-select questions, ensure we have either an answer value or selected options
                if (answer.Question?.QuestionType == Domain.Enums.QuestionType.MultiSelect)
                {
                    if (selectedOptionIds.Count == 0 && string.IsNullOrEmpty(answerValue))
                    {
                        _logger.LogWarning("Multi-select question {QuestionId} has no selected options or answer value", answer.QuestionId);
                        continue; // Skip this answer as it's incomplete
                    }

                    // For multi-select, ensure we have an answer value even if it's generic
                    if (string.IsNullOrEmpty(answerValue) && selectedOptionIds.Count > 0)
                    {
                        answerValue = "Multiple options selected";
                    }
                }
                else
                {
                    // For non-multi-select questions, clear selected options to avoid validation errors
                    selectedOptionIds = new List<int>();

                    // Ensure we have an answer value for required questions
                    if (string.IsNullOrEmpty(answerValue) && !answer.IsNotApplicable)
                    {
                        _logger.LogWarning("Question {QuestionId} has no answer value and is not marked as N/A", answer.QuestionId);
                        continue; // Skip this answer as it's incomplete
                    }
                }

                // Create the command in the format expected by the API
                var command = new
                {
                    AuditId = auditId,
                    QuestionId = answer.QuestionId,
                    AnswerValue = answerValue,
                    IsNotApplicable = answer.IsNotApplicable,
                    Comments = answer.Comments,
                    SelectedOptionIds = selectedOptionIds,
                    FailureReasonTexts = failureReasonTexts, // Send the actual failure reason texts for backward compatibility
                    SeverityLevel = (int?)answer.SeverityLevel,
                    EnhancedFindings = enhancedFindings // Send enhanced finding data with severity and immediate actions
                };

                var response = await _httpClient.PostAsJsonAsync($"api/v1/audits/{auditId}/answers", command);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Failed to submit answer for question {QuestionId}. Status: {StatusCode}, Content: {Content}",
                        answer.QuestionId, response.StatusCode, errorContent);
                    return Result.Failure($"Failed to submit answer for question {answer.QuestionId}");
                }
            }

            _logger.LogInformation("Successfully submitted all answers for audit: {AuditId}", auditId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting answers for audit: {AuditId}", auditId);
            return Result.Failure("An error occurred while submitting audit answers");
        }
    }

    /// <summary>
    /// Converts an AuditAnswer to a string value based on the answer type
    /// </summary>
    private static string? GetAnswerValueString(AuditAnswer answer)
    {
        // If marked as Not Applicable, return N/A
        if (answer.IsNotApplicable)
            return "N/A";

        // Return the first non-null answer value as a string
        if (answer.AnswerBoolean.HasValue)
            return answer.AnswerBoolean.Value ? "Yes" : "No";

        if (!string.IsNullOrWhiteSpace(answer.AnswerText))
            return answer.AnswerText.Trim();

        if (answer.AnswerNumeric.HasValue)
            return answer.AnswerNumeric.Value.ToString("G"); // General format

        if (answer.AnswerDate.HasValue)
            return answer.AnswerDate.Value.ToString("yyyy-MM-dd");

        if (answer.SelectedOptionId.HasValue)
            return answer.SelectedOptionId.Value.ToString();

        // For multi-select questions, the SelectedOptionIds will be handled separately
        // Return a generic value to satisfy the required field if we have selected options
        if (answer.GetSelectedOptionIds()?.Any() == true)
            return "Multiple options selected";

        return null;
    }

    public async Task<Result> CompleteAuditAsync(string auditId)
    {
        try
        {
            _logger.LogInformation("Completing audit: {AuditId}", auditId);

            var request = new SubmitAuditRequest
            {
                AuditorComments = null // Can be extended to include comments
            };

            var response = await _httpClient.PostAsJsonAsync($"api/v1/audits/{auditId}/submit", request);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Successfully completed audit: {AuditId}", auditId);
                return Result.Success();
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to complete audit. Status: {StatusCode}, Content: {Content}",
                response.StatusCode, errorContent);

            return Result.Failure("Failed to complete audit");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing audit: {AuditId}", auditId);
            return Result.Failure("An error occurred while completing the audit");
        }
    }

    public async Task<Result<string>> UploadAttachmentAsync(string auditId, string questionId, byte[] fileData, string fileName, string contentType)
    {
        try
        {
            _logger.LogInformation("Uploading attachment for audit: {AuditId}, question: {QuestionId}, file: {FileName}",
                auditId, questionId, fileName);

            using var content = new MultipartFormDataContent();
            using var fileContent = new ByteArrayContent(fileData);
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(contentType);
            content.Add(fileContent, "file", fileName);

            var response = await _httpClient.PostAsync("api/v1/files/upload", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<FileUploadResult>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    _logger.LogInformation("Successfully uploaded attachment: {FilePath}", apiResponse.Data.FilePath);
                    return Result<string>.Success(apiResponse.Data.FilePath);
                }
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            _logger.LogWarning("Failed to upload attachment. Status: {StatusCode}, Content: {Content}",
                response.StatusCode, errorContent);

            return Result<string>.Failure("Failed to upload attachment");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading attachment for audit: {AuditId}", auditId);
            return Result<string>.Failure("An error occurred while uploading the attachment");
        }
    }

    /// <summary>
    /// Maps AuditDto from API to domain Audit entity
    /// </summary>
    private static Audit MapAuditDtoToEntity(AuditDto dto)
    {
        var audit = new Audit
        {
            Id = dto.Id,
            AuditTemplateId = dto.AuditTemplateId,
            AssignmentType = dto.AssignmentType,
            AssignedToUserGroupId = dto.AssignedToUserGroupId,
            AssignedToUserId = dto.AssignedToUserId,
            ScheduledDate = dto.ScheduledDate,
            DueDate = dto.DueDate,
            StartedAt = dto.StartedAt,
            CompletedAt = dto.CompletedAt,
            OverallStatus = dto.OverallStatus,
            FactoryId = dto.FactoryId,
            AreaId = dto.AreaId,
            SubAreaId = dto.SubAreaId,
            OverallScore = dto.OverallScore,
            ManagerComments = dto.ManagerComments,
            ReviewedByUserId = dto.ReviewedByUserId,
            ReviewedAt = dto.ReviewedAt,
            RecurringAuditSettingId = dto.RecurringAuditSettingId,
            CreatedAt = dto.CreatedAt,
            UpdatedAt = dto.UpdatedAt,
            CreatedByUserId = dto.CreatedByUserId,
            UpdatedByUserId = dto.UpdatedByUserId,
            RecordVersion = dto.RecordVersion
        };

        // Create audit template with questions
        audit.AuditTemplate = new AuditTemplate
        {
            Id = dto.AuditTemplateId,
            TemplateName = dto.AuditTemplateName ?? "",
            Questions = MapQuestionDtosToEntities(dto.Questions),
            QuestionGroups = MapQuestionGroupDtosToEntities(dto.QuestionGroups)
        };

        // Create related entities
        if (!string.IsNullOrEmpty(dto.FactoryName))
        {
            audit.Factory = new Factory { Id = dto.FactoryId, FactoryName = dto.FactoryName };
        }

        if (!string.IsNullOrEmpty(dto.AreaName))
        {
            audit.Area = new Area { Id = dto.AreaId, AreaName = dto.AreaName };
        }

        if (dto.SubAreaId.HasValue && !string.IsNullOrEmpty(dto.SubAreaName))
        {
            audit.SubArea = new SubArea { Id = dto.SubAreaId.Value, SubAreaName = dto.SubAreaName };
        }

        return audit;
    }

    /// <summary>
    /// Maps QuestionDto list to Question entity list
    /// </summary>
    private static ICollection<Question> MapQuestionDtosToEntities(List<QuestionDto> dtos)
    {
        return dtos.Select(dto => new Question
        {
            Id = dto.Id,
            AuditTemplateId = dto.AuditTemplateId,
            QuestionGroupId = dto.QuestionGroupId,
            QuestionText = dto.QuestionText,
            QuestionType = dto.QuestionType,
            DisplayOrder = dto.DisplayOrder,
            IsRequired = dto.IsRequired,
            Weight = dto.Weight,
            HelpText = dto.HelpText,
            ParentQuestionId = dto.ParentQuestionId,
            TriggerAnswerValue = dto.TriggerAnswerValue,
            SeverityLevel = dto.SeverityLevel,
            EvidenceRequired = dto.EvidenceRequired,
            AllowedEvidenceTypes = dto.AllowedEvidenceTypes?.Select((et, index) => new QuestionAllowedEvidenceType
            {
                EvidenceType = et.EvidenceType,
                DisplayOrder = et.DisplayOrder,
                IsActive = et.IsActive
            }).ToList() ?? new List<QuestionAllowedEvidenceType>(),
            IsActive = dto.IsActive,
            Options = MapQuestionOptionDtosToEntities(dto.Options),
            ChildQuestions = MapQuestionDtosToEntities(dto.ChildQuestions)
        }).ToList();
    }

    /// <summary>
    /// Maps QuestionGroupDto list to QuestionGroup entity list
    /// </summary>
    private static ICollection<QuestionGroup> MapQuestionGroupDtosToEntities(List<QuestionGroupDto> dtos)
    {
        return dtos.Select(dto => new QuestionGroup
        {
            Id = dto.Id,
            AuditTemplateId = dto.AuditTemplateId,
            GroupName = dto.GroupName,
            Description = dto.Description,
            DisplayOrder = dto.DisplayOrder,
            IsActive = dto.IsActive,
            Questions = MapQuestionDtosToEntities(dto.Questions)
        }).ToList();
    }

    /// <summary>
    /// Maps QuestionOptionDto list to QuestionOption entity list
    /// </summary>
    private static ICollection<QuestionOption> MapQuestionOptionDtosToEntities(List<QuestionOptionDto> dtos)
    {
        return dtos.Select(dto => new QuestionOption
        {
            Id = dto.Id,
            QuestionId = dto.QuestionId,
            OptionText = dto.OptionText,
            OptionValue = dto.OptionValue,
            DisplayOrder = dto.DisplayOrder,
            IsActive = dto.IsActive
        }).ToList();
    }

    /// <summary>
    /// Maps AuditReviewDto from API to AuditSummaryDto for PWA
    /// </summary>
    private static Models.AuditSummaryDto MapAuditReviewDtoToSummary(Application.Audits.DTOs.AuditReviewDto dto)
    {
        return new Models.AuditSummaryDto
        {
            Id = dto.Id,
            AuditTemplateName = dto.AuditTemplateName,
            AssignedToUserName = dto.AssignedToUserName,
            ScheduledDate = dto.ScheduledDate,
            DueDate = dto.DueDate,
            OverallStatus = dto.OverallStatus,
            FactoryName = dto.FactoryName,
            AreaName = dto.AreaName,
            SubAreaName = dto.SubAreaName,
            OverallScore = dto.OverallScore.HasValue ? (double)dto.OverallScore.Value : null,
            IsOverdue = dto.IsOverdue,
            StartedAt = dto.StartedAt,
            CompletedAt = dto.CompletedAt,
            TotalQuestions = dto.TotalQuestions,
            AnsweredQuestions = dto.AnsweredQuestions,
            EvidenceCount = dto.TotalAttachments,
            NonConformanceCount = dto.QuestionsWithFindings,
            PassedQuestions = dto.PassedQuestions,
            FailedQuestions = dto.FailedQuestions,
            NotApplicableQuestions = dto.NotApplicableQuestions,
            ManagerComments = dto.ManagerComments,
            ReviewedByUserName = dto.ReviewedByUserName,
            ReviewedAt = dto.ReviewedAt
        };
    }

    public async Task<Result<PaginatedResult<Models.AuditSummaryDto>>> GetMyAuditHistoryAsync(int pageNumber = 1, int pageSize = 20, string? searchTerm = null, string? status = null, DateTime? completedDateFrom = null, DateTime? completedDateTo = null)
    {
        try
        {
            _logger.LogInformation("Getting audit history for current user");

            var queryParams = new List<string>
            {
                $"pageNumber={pageNumber}",
                $"pageSize={pageSize}"
            };

            if (!string.IsNullOrEmpty(searchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(searchTerm)}");

            if (!string.IsNullOrEmpty(status))
                queryParams.Add($"status={Uri.EscapeDataString(status)}");

            if (completedDateFrom.HasValue)
                queryParams.Add($"completedDateFrom={completedDateFrom.Value:yyyy-MM-dd}");

            if (completedDateTo.HasValue)
                queryParams.Add($"completedDateTo={completedDateTo.Value:yyyy-MM-dd}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"api/v1/audits/my-history?{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<PaginatedResult<Models.AuditSummaryDto>>>();
                if (result?.Success == true && result.Data != null)
                {
                    _logger.LogInformation("Successfully retrieved {Count} audit history items", result.Data.Items.Count);
                    return Result<PaginatedResult<Models.AuditSummaryDto>>.Success(result.Data);
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", result?.Message);
                    return Result<PaginatedResult<Models.AuditSummaryDto>>.Failure(result?.Message ?? "Failed to get audit history");
                }
            }
            else
            {
                _logger.LogWarning("Failed to get audit history. Status: {Status}", response.StatusCode);
                return Result<PaginatedResult<Models.AuditSummaryDto>>.Failure($"Failed to get audit history: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting audit history");
            return Result<PaginatedResult<Models.AuditSummaryDto>>.Failure($"Error getting audit history: {ex.Message}");
        }
    }
}

/// <summary>
/// Stub implementation of template API service
/// </summary>
public class TemplateApiService : ITemplateApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<TemplateApiService> _logger;

    public TemplateApiService(HttpClient httpClient, ILogger<TemplateApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public Task<Result<List<AuditTemplate>>> GetAvailableTemplatesAsync()
    {
        // TODO: Implement actual API call
        return Task.FromResult(Result<List<AuditTemplate>>.Success(new List<AuditTemplate>()));
    }

    public Task<Result<AuditTemplate?>> GetTemplateAsync(int templateId)
    {
        // TODO: Implement actual API call
        return Task.FromResult(Result<AuditTemplate?>.Success(null));
    }
}

/// <summary>
/// Stub implementation of user API service
/// </summary>
public class UserApiService : IUserApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<UserApiService> _logger;

    public UserApiService(HttpClient httpClient, ILogger<UserApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public Task<Result<User?>> GetCurrentUserProfileAsync()
    {
        // TODO: Implement actual API call
        return Task.FromResult(Result<User?>.Success(null));
    }

    public Task<Result> UpdateUserProfileAsync(User user)
    {
        // TODO: Implement actual API call
        return Task.FromResult(Result.Success());
    }
}

/// <summary>
/// Implementation of offline storage service using local storage
/// </summary>
public class OfflineStorageService : IOfflineStorageService
{
    private readonly ILocalStorageService _localStorage;
    private readonly ILogger<OfflineStorageService> _logger;
    private const string AuditsKey = "offline_audits";
    private const string AnswersKey = "offline_answers";
    private const string AttachmentsKey = "offline_attachments";

    public OfflineStorageService(ILocalStorageService localStorage, ILogger<OfflineStorageService> logger)
    {
        _localStorage = localStorage;
        _logger = logger;
    }

    public async Task StoreAuditAsync(Audit audit)
    {
        try
        {
            var audits = await GetOfflineAuditsAsync();

            // Remove existing audit if present
            audits.RemoveAll(a => a.Id == audit.Id);

            // Add updated audit
            audits.Add(audit);

            await _localStorage.SetItemAsync(AuditsKey, audits);
            _logger.LogInformation("Stored audit offline: {AuditId}", audit.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing audit offline: {AuditId}", audit.Id);
        }
    }

    public async Task<List<Audit>> GetOfflineAuditsAsync()
    {
        try
        {
            var audits = await _localStorage.GetItemAsync<List<Audit>>(AuditsKey);
            return audits ?? new List<Audit>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving offline audits");
            return new List<Audit>();
        }
    }

    public async Task RemoveOfflineAuditAsync(string auditId)
    {
        try
        {
            var audits = await GetOfflineAuditsAsync();
            audits.RemoveAll(a => a.Id == auditId);
            await _localStorage.SetItemAsync(AuditsKey, audits);

            // Also remove associated answers
            var answers = await GetOfflineAuditAnswersAsync(auditId);
            if (answers.Any())
            {
                var allAnswers = await _localStorage.GetItemAsync<Dictionary<string, List<AuditAnswer>>>(AnswersKey) ?? new Dictionary<string, List<AuditAnswer>>();
                allAnswers.Remove(auditId);
                await _localStorage.SetItemAsync(AnswersKey, allAnswers);
            }

            _logger.LogInformation("Removed offline audit: {AuditId}", auditId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing offline audit: {AuditId}", auditId);
        }
    }

    public async Task StoreAuditAnswersAsync(string auditId, List<AuditAnswer> answers)
    {
        try
        {
            var allAnswers = await _localStorage.GetItemAsync<Dictionary<string, List<AuditAnswer>>>(AnswersKey) ?? new Dictionary<string, List<AuditAnswer>>();
            allAnswers[auditId] = answers;
            await _localStorage.SetItemAsync(AnswersKey, allAnswers);

            _logger.LogInformation("Stored {Count} answers offline for audit: {AuditId}", answers.Count, auditId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing audit answers offline: {AuditId}", auditId);
        }
    }

    public async Task<List<AuditAnswer>> GetOfflineAuditAnswersAsync(string auditId)
    {
        try
        {
            var allAnswers = await _localStorage.GetItemAsync<Dictionary<string, List<AuditAnswer>>>(AnswersKey) ?? new Dictionary<string, List<AuditAnswer>>();
            return allAnswers.TryGetValue(auditId, out var answers) ? answers : new List<AuditAnswer>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving offline audit answers: {AuditId}", auditId);
            return new List<AuditAnswer>();
        }
    }

    public async Task StoreAttachmentAsync(OfflineAttachment attachment)
    {
        try
        {
            var attachments = await _localStorage.GetItemAsync<List<OfflineAttachment>>(AttachmentsKey) ?? new List<OfflineAttachment>();

            // Remove existing attachment if present
            attachments.RemoveAll(a => a.Id == attachment.Id);

            // Add new/updated attachment
            attachments.Add(attachment);

            await _localStorage.SetItemAsync(AttachmentsKey, attachments);
            _logger.LogInformation("Stored attachment offline: {AttachmentId}, Size: {Size} bytes", attachment.Id, attachment.Size);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing attachment offline: {AttachmentId}", attachment.Id);
        }
    }

    public async Task<List<OfflineAttachment>> GetOfflineAttachmentsAsync(string auditId)
    {
        try
        {
            var attachments = await _localStorage.GetItemAsync<List<OfflineAttachment>>(AttachmentsKey) ?? new List<OfflineAttachment>();
            return attachments.Where(a => a.AuditId == auditId).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving offline attachments for audit: {AuditId}", auditId);
            return new List<OfflineAttachment>();
        }
    }

    public async Task<List<OfflineAttachment>> GetPendingAttachmentsAsync()
    {
        try
        {
            var attachments = await _localStorage.GetItemAsync<List<OfflineAttachment>>(AttachmentsKey) ?? new List<OfflineAttachment>();
            return attachments.Where(a => !a.IsUploaded).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving pending attachments");
            return new List<OfflineAttachment>();
        }
    }

    public async Task UpdateAttachmentAsync(OfflineAttachment attachment)
    {
        try
        {
            var attachments = await _localStorage.GetItemAsync<List<OfflineAttachment>>(AttachmentsKey) ?? new List<OfflineAttachment>();
            var index = attachments.FindIndex(a => a.Id == attachment.Id);

            if (index >= 0)
            {
                attachments[index] = attachment;
                await _localStorage.SetItemAsync(AttachmentsKey, attachments);
                _logger.LogInformation("Updated attachment: {AttachmentId}", attachment.Id);
            }
            else
            {
                _logger.LogWarning("Attachment not found for update: {AttachmentId}", attachment.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating attachment: {AttachmentId}", attachment.Id);
        }
    }

    public async Task RemoveAttachmentAsync(string attachmentId)
    {
        try
        {
            var attachments = await _localStorage.GetItemAsync<List<OfflineAttachment>>(AttachmentsKey) ?? new List<OfflineAttachment>();
            var removed = attachments.RemoveAll(a => a.Id == attachmentId);

            if (removed > 0)
            {
                await _localStorage.SetItemAsync(AttachmentsKey, attachments);
                _logger.LogInformation("Removed attachment: {AttachmentId}", attachmentId);
            }
            else
            {
                _logger.LogWarning("Attachment not found for removal: {AttachmentId}", attachmentId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing attachment: {AttachmentId}", attachmentId);
        }
    }
}

/// <summary>
/// Stub implementation of sync service
/// </summary>
public class SyncService : ISyncService
{
    private readonly ILogger<SyncService> _logger;

    public SyncService(ILogger<SyncService> logger)
    {
        _logger = logger;
    }

    public Task<Result> SyncAsync()
    {
        // TODO: Implement sync logic
        return Task.FromResult(Result.Success());
    }

    public Task<bool> IsSyncInProgressAsync()
    {
        // TODO: Implement sync status
        return Task.FromResult(false);
    }

    public Task<int> GetPendingSyncItemsCountAsync()
    {
        // TODO: Implement pending items count
        return Task.FromResult(0);
    }

    public Task<SyncStatus> GetSyncStatusAsync()
    {
        // TODO: Implement sync status
        return Task.FromResult(new SyncStatus
        {
            IsOnline = true,
            IsSyncInProgress = false,
            PendingAudits = 0,
            PendingAnswers = 0,
            PendingAttachments = 0,
            LastSyncTime = null,
            LastSyncError = null
        });
    }

    public Task<Result> QueueForBackgroundSyncAsync<T>(T data, string syncType)
    {
        // TODO: Implement background sync queueing
        _logger.LogInformation("Queuing data for background sync: {SyncType}", syncType);
        return Task.FromResult(Result.Success());
    }

    public Task<Result> RequestManualSyncAsync(string syncType)
    {
        // TODO: Implement manual sync request
        _logger.LogInformation("Requesting manual sync: {SyncType}", syncType);
        return Task.FromResult(Result.Success());
    }

    public Task<List<BackgroundSyncItem>> GetPendingSyncItemsAsync(string? syncType = null)
    {
        // TODO: Implement getting pending sync items
        return Task.FromResult(new List<BackgroundSyncItem>());
    }
}

/// <summary>
/// Implementation of PWA service
/// </summary>
public class PwaService : IPwaService
{
    private readonly IJSRuntime _jsRuntime;
    private readonly ILogger<PwaService> _logger;
    private readonly List<Func<bool, Task>> _networkStatusCallbacks = new();

    public PwaService(IJSRuntime jsRuntime, ILogger<PwaService> logger)
    {
        _jsRuntime = jsRuntime;
        _logger = logger;
    }

    public async Task<bool> IsPwaAsync()
    {
        try
        {
            var isPwa = await _jsRuntime.InvokeAsync<bool>("isPwaMode");
            return isPwa;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking PWA mode");
            return false;
        }
    }

    public async Task<bool> CanInstallAsync()
    {
        try
        {
            var canInstall = await _jsRuntime.InvokeAsync<bool>("canInstallPwa");
            return canInstall;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking install capability");
            return false;
        }
    }

    public async Task PromptInstallAsync()
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("promptInstall");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error prompting install");
        }
    }

    public async Task<bool> IsOnlineAsync()
    {
        try
        {
            var isOnline = await _jsRuntime.InvokeAsync<bool>("isOnline");
            return isOnline;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking online status");
            return true; // Assume online if we can't check
        }
    }

    public async Task RegisterNetworkStatusChangedAsync(Func<bool, Task> callback)
    {
        try
        {
            _networkStatusCallbacks.Add(callback);

            // Register JavaScript callback if this is the first registration
            if (_networkStatusCallbacks.Count == 1)
            {
                var dotNetRef = DotNetObjectReference.Create(this);
                await _jsRuntime.InvokeVoidAsync("registerNetworkStatusCallback", dotNetRef);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering network status callback");
        }
    }

    [JSInvokable]
    public async Task OnNetworkStatusChanged(bool isOnline)
    {
        try
        {
            _logger.LogInformation("Network status changed: {IsOnline}", isOnline);

            foreach (var callback in _networkStatusCallbacks)
            {
                try
                {
                    await callback(isOnline);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in network status callback");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling network status change");
        }
    }
}

/// <summary>
/// Stub implementation of camera service
/// </summary>
public class CameraService : ICameraService
{
    private readonly ILogger<CameraService> _logger;

    public CameraService(ILogger<CameraService> logger)
    {
        _logger = logger;
    }

    public Task<bool> IsCameraAvailableAsync()
    {
        // TODO: Implement camera availability check
        return Task.FromResult(false);
    }

    public Task<Result<byte[]>> CapturePhotoAsync()
    {
        // TODO: Implement photo capture
        return Task.FromResult(Result<byte[]>.Failure("Camera not implemented"));
    }

    public Task<Result<byte[]>> SelectPhotoAsync()
    {
        // TODO: Implement photo selection
        return Task.FromResult(Result<byte[]>.Failure("Photo selection not implemented"));
    }

    public Task<byte[]> ResizeImageAsync(byte[] imageData, int maxWidth, int maxHeight, int quality = 80)
    {
        // TODO: Implement image resizing
        return Task.FromResult(imageData);
    }
}

/// <summary>
/// Implementation of background sync service
/// </summary>
public class BackgroundSyncService : IBackgroundSyncService
{
    private readonly IJSRuntime _jsRuntime;
    private readonly ILogger<BackgroundSyncService> _logger;

    public BackgroundSyncService(IJSRuntime jsRuntime, ILogger<BackgroundSyncService> logger)
    {
        _jsRuntime = jsRuntime;
        _logger = logger;
    }

    public async Task<bool> IsBackgroundSyncSupportedAsync()
    {
        try
        {
            var isSupported = await _jsRuntime.InvokeAsync<bool>("isBackgroundSyncSupported");
            return isSupported;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking background sync support");
            return false;
        }
    }

    public async Task<bool> QueueForBackgroundSyncAsync<T>(T data, string syncType)
    {
        try
        {
            _logger.LogInformation("Queuing data for background sync: {SyncType}", syncType);

            var success = await _jsRuntime.InvokeAsync<bool>("queueForBackgroundSync", data, syncType);

            if (success)
            {
                _logger.LogInformation("Successfully queued data for background sync: {SyncType}", syncType);
            }
            else
            {
                _logger.LogWarning("Failed to queue data for background sync: {SyncType}", syncType);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error queuing data for background sync: {SyncType}", syncType);
            return false;
        }
    }

    public async Task<List<BackgroundSyncItem>> GetPendingSyncItemsAsync(string? syncType = null)
    {
        try
        {
            var items = await _jsRuntime.InvokeAsync<BackgroundSyncItem[]>("getPendingSyncItems", syncType);
            return items?.ToList() ?? new List<BackgroundSyncItem>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending sync items");
            return new List<BackgroundSyncItem>();
        }
    }

    public async Task RemoveSyncItemAsync(int itemId)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("removeSyncItem", itemId);
            _logger.LogInformation("Removed sync item: {ItemId}", itemId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing sync item: {ItemId}", itemId);
        }
    }

    public async Task<bool> RegisterBackgroundSyncAsync(string tag)
    {
        try
        {
            var success = await _jsRuntime.InvokeAsync<bool>("registerBackgroundSync", tag);

            if (success)
            {
                _logger.LogInformation("Successfully registered background sync: {Tag}", tag);
            }
            else
            {
                _logger.LogWarning("Failed to register background sync: {Tag}", tag);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering background sync: {Tag}", tag);
            return false;
        }
    }

    public async Task RequestManualSyncAsync(string syncType)
    {
        try
        {
            _logger.LogInformation("Requesting manual sync: {SyncType}", syncType);

            await _jsRuntime.InvokeVoidAsync("requestManualSync", syncType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting manual sync: {SyncType}", syncType);
        }
    }
}

/// <summary>
/// Implementation of cache management service
/// </summary>
public class CacheManagementService : ICacheManagementService
{
    private readonly IJSRuntime _jsRuntime;
    private readonly ILogger<CacheManagementService> _logger;

    public CacheManagementService(IJSRuntime jsRuntime, ILogger<CacheManagementService> logger)
    {
        _jsRuntime = jsRuntime;
        _logger = logger;
    }

    public async Task<bool> ClearOldCachesAsync()
    {
        try
        {
            _logger.LogInformation("Clearing old caches");

            var success = await _jsRuntime.InvokeAsync<bool>("clearOldCaches");

            if (success)
            {
                _logger.LogInformation("Successfully cleared old caches");
            }
            else
            {
                _logger.LogWarning("Failed to clear old caches");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing old caches");
            return false;
        }
    }

    public async Task<CacheStorageInfo> GetCacheStorageUsageAsync()
    {
        try
        {
            var usage = await _jsRuntime.InvokeAsync<CacheStorageInfo>("getCacheStorageUsage");
            return usage ?? new CacheStorageInfo();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache storage usage");
            return new CacheStorageInfo();
        }
    }

    public async Task<bool> PreloadCriticalResourcesAsync(string[] resources)
    {
        try
        {
            _logger.LogInformation("Preloading {Count} critical resources", resources.Length);

            var success = await _jsRuntime.InvokeAsync<bool>("preloadCriticalResources", resources);

            if (success)
            {
                _logger.LogInformation("Successfully preloaded critical resources");
            }
            else
            {
                _logger.LogWarning("Failed to preload critical resources");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error preloading critical resources");
            return false;
        }
    }

    public async Task<bool> IsResourceCachedAsync(string url)
    {
        try
        {
            var isCached = await _jsRuntime.InvokeAsync<bool>("isResourceCached", url);
            return isCached;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if resource is cached: {Url}", url);
            return false;
        }
    }

    public async Task<bool> UpdateCacheAsync(string url, object data)
    {
        try
        {
            _logger.LogInformation("Updating cache for: {Url}", url);

            var success = await _jsRuntime.InvokeAsync<bool>("updateCache", url, data);

            if (success)
            {
                _logger.LogInformation("Successfully updated cache for: {Url}", url);
            }
            else
            {
                _logger.LogWarning("Failed to update cache for: {Url}", url);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating cache for: {Url}", url);
            return false;
        }
    }
}

/// <summary>
/// Implementation of correction request service
/// </summary>
public class CorrectionRequestService : ICorrectionRequestService
{
    private readonly AuthenticatedHttpClientService _httpClient;
    private readonly ILogger<CorrectionRequestService> _logger;
    private readonly IOfflineStorageService _offlineStorage;

    public CorrectionRequestService(
        AuthenticatedHttpClientService httpClient,
        ILogger<CorrectionRequestService> logger,
        IOfflineStorageService offlineStorage)
    {
        _httpClient = httpClient;
        _logger = logger;
        _offlineStorage = offlineStorage;
    }

    public async Task<Result<string>> SubmitCorrectionRequestAsync(CorrectionRequestDto request)
    {
        try
        {
            _logger.LogInformation("Submitting correction request for audit: {AuditId}", request.AuditId);

            var response = await _httpClient.PostAsJsonAsync("api/correction-requests", request);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<string>>();
                if (result?.Success == true && result.Data != null)
                {
                    _logger.LogInformation("Successfully submitted correction request: {RequestId}", result.Data);
                    return Result<string>.Success(result.Data);
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", result?.Message);
                    return Result<string>.Failure(result?.Message ?? "Failed to submit correction request");
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to submit correction request. Status: {Status}, Content: {Content}",
                    response.StatusCode, errorContent);
                return Result<string>.Failure($"Failed to submit correction request: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting correction request for audit: {AuditId}", request.AuditId);
            return Result<string>.Failure($"Error submitting correction request: {ex.Message}");
        }
    }

    public async Task<Result<List<CorrectionRequestDto>>> GetCorrectionRequestsAsync(string auditId)
    {
        try
        {
            _logger.LogInformation("Getting correction requests for audit: {AuditId}", auditId);

            var response = await _httpClient.GetAsync($"api/correction-requests/audit/{auditId}");

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<List<CorrectionRequestDto>>>();
                if (result?.Success == true && result.Data != null)
                {
                    _logger.LogInformation("Successfully retrieved {Count} correction requests for audit: {AuditId}",
                        result.Data.Count, auditId);
                    return Result<List<CorrectionRequestDto>>.Success(result.Data);
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", result?.Message);
                    return Result<List<CorrectionRequestDto>>.Failure(result?.Message ?? "Failed to get correction requests");
                }
            }
            else
            {
                _logger.LogWarning("Failed to get correction requests. Status: {Status}", response.StatusCode);
                return Result<List<CorrectionRequestDto>>.Failure($"Failed to get correction requests: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting correction requests for audit: {AuditId}", auditId);
            return Result<List<CorrectionRequestDto>>.Failure($"Error getting correction requests: {ex.Message}");
        }
    }

    public async Task<Result<List<CorrectionRequestDto>>> GetMyCorrectionRequestsAsync()
    {
        try
        {
            _logger.LogInformation("Getting my correction requests");

            var response = await _httpClient.GetAsync("api/v1/correction-requests/my-requests?pageSize=100");

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<PaginatedResult<CorrectionRequestSummaryDto>>>();
                if (result?.Success == true && result.Data != null)
                {
                    // Convert summary DTOs to full DTOs for compatibility
                    var correctionRequests = result.Data.Items.Select(summary => new CorrectionRequestDto
                    {
                        Id = summary.Id,
                        AuditId = summary.AuditId,
                        AuditTemplateName = summary.AuditTemplateName,
                        FactoryName = summary.FactoryName,
                        AreaName = summary.AreaName,
                        RequestedAt = summary.RequestedAt,
                        Status = summary.Status,
                        ReviewedByUserName = summary.ReviewedByUserName,
                        ReviewedAt = summary.ReviewedAt
                    }).ToList();

                    _logger.LogInformation("Successfully retrieved {Count} correction requests", correctionRequests.Count);
                    return Result<List<CorrectionRequestDto>>.Success(correctionRequests);
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", result?.Message);
                    return Result<List<CorrectionRequestDto>>.Failure(result?.Message ?? "Failed to get correction requests");
                }
            }
            else
            {
                _logger.LogWarning("Failed to get my correction requests. Status: {Status}", response.StatusCode);
                return Result<List<CorrectionRequestDto>>.Failure($"Failed to get correction requests: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting my correction requests");
            return Result<List<CorrectionRequestDto>>.Failure($"Error getting correction requests: {ex.Message}");
        }
    }

    public async Task<Result<List<CorrectionRequestDto>>> GetPendingCorrectionRequestsAsync()
    {
        try
        {
            _logger.LogInformation("Getting pending correction requests for review");

            var response = await _httpClient.GetAsync("api/correction-requests/pending");

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<List<CorrectionRequestDto>>>();
                if (result?.Success == true && result.Data != null)
                {
                    _logger.LogInformation("Successfully retrieved {Count} pending correction requests", result.Data.Count);
                    return Result<List<CorrectionRequestDto>>.Success(result.Data);
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", result?.Message);
                    return Result<List<CorrectionRequestDto>>.Failure(result?.Message ?? "Failed to get pending correction requests");
                }
            }
            else
            {
                _logger.LogWarning("Failed to get pending correction requests. Status: {Status}", response.StatusCode);
                return Result<List<CorrectionRequestDto>>.Failure($"Failed to get pending correction requests: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending correction requests");
            return Result<List<CorrectionRequestDto>>.Failure($"Error getting pending correction requests: {ex.Message}");
        }
    }

    public async Task<Result> ApproveCorrectionRequestAsync(string requestId, string managerComments = "")
    {
        try
        {
            _logger.LogInformation("Approving correction request: {RequestId}", requestId);

            var request = new { ManagerComments = managerComments };
            var response = await _httpClient.PostAsJsonAsync($"api/correction-requests/{requestId}/approve", request);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<object>>();
                if (result?.Success == true)
                {
                    _logger.LogInformation("Successfully approved correction request: {RequestId}", requestId);
                    return Result.Success();
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", result?.Message);
                    return Result.Failure(result?.Message ?? "Failed to approve correction request");
                }
            }
            else
            {
                _logger.LogWarning("Failed to approve correction request. Status: {Status}", response.StatusCode);
                return Result.Failure($"Failed to approve correction request: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving correction request: {RequestId}", requestId);
            return Result.Failure($"Error approving correction request: {ex.Message}");
        }
    }

    public async Task<Result> DenyCorrectionRequestAsync(string requestId, string managerComments)
    {
        try
        {
            _logger.LogInformation("Denying correction request: {RequestId}", requestId);

            var request = new { ManagerComments = managerComments };
            var response = await _httpClient.PostAsJsonAsync($"api/correction-requests/{requestId}/deny", request);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<object>>();
                if (result?.Success == true)
                {
                    _logger.LogInformation("Successfully denied correction request: {RequestId}", requestId);
                    return Result.Success();
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", result?.Message);
                    return Result.Failure(result?.Message ?? "Failed to deny correction request");
                }
            }
            else
            {
                _logger.LogWarning("Failed to deny correction request. Status: {Status}", response.StatusCode);
                return Result.Failure($"Failed to deny correction request: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error denying correction request: {RequestId}", requestId);
            return Result.Failure($"Error denying correction request: {ex.Message}");
        }
    }

    public async Task<Result<bool>> CanRequestCorrectionAsync(string auditId)
    {
        try
        {
            _logger.LogInformation("Checking if correction can be requested for audit: {AuditId}", auditId);

            var response = await _httpClient.GetAsync($"api/correction-requests/can-request/{auditId}");

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<bool>>();
                if (result?.Success == true)
                {
                    _logger.LogInformation("Can request correction for audit {AuditId}: {CanRequest}", auditId, result.Data);
                    return Result<bool>.Success(result.Data);
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", result?.Message);
                    return Result<bool>.Failure(result?.Message ?? "Failed to check correction eligibility");
                }
            }
            else
            {
                _logger.LogWarning("Failed to check correction eligibility. Status: {Status}", response.StatusCode);
                return Result<bool>.Failure($"Failed to check correction eligibility: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking correction eligibility for audit: {AuditId}", auditId);
            return Result<bool>.Failure($"Error checking correction eligibility: {ex.Message}");
        }
    }
}

/// <summary>
/// Implementation of finding category service for PWA
/// </summary>
public class FindingCategoryService : IFindingCategoryService
{
    private readonly AuthenticatedHttpClientService _httpClient;
    private readonly ILogger<FindingCategoryService> _logger;

    public FindingCategoryService(AuthenticatedHttpClientService httpClient, ILogger<FindingCategoryService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<Result<List<FindingCategorySummaryModel>>> GetCategoriesForTemplateAsync(int auditTemplateId)
    {
        try
        {
            _logger.LogInformation("Getting finding categories for template {TemplateId}", auditTemplateId);

            var response = await _httpClient.GetAsync($"api/v1/findings/categories?auditTemplateId={auditTemplateId}&isActive=true");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<FindingCategoryModel>>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    var summaries = apiResponse.Data.Select(c => new FindingCategorySummaryModel
                    {
                        Id = c.Id,
                        CategoryName = c.CategoryName,
                        Description = c.Description,
                        ColorCode = c.ColorCode,
                        IconName = c.IconName,
                        IsActive = c.IsActive,
                        FindingCount = c.FindingCount
                    }).ToList();

                    return Result<List<FindingCategorySummaryModel>>.Success(summaries);
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", apiResponse?.Message);
                    return Result<List<FindingCategorySummaryModel>>.Failure(apiResponse?.Message ?? "Failed to get finding categories");
                }
            }
            else
            {
                _logger.LogWarning("Failed to get finding categories. Status: {Status}", response.StatusCode);
                return Result<List<FindingCategorySummaryModel>>.Failure($"Failed to get finding categories: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting finding categories for template: {TemplateId}", auditTemplateId);
            return Result<List<FindingCategorySummaryModel>>.Failure($"Error getting finding categories: {ex.Message}");
        }
    }

    public async Task<Result> AssignCategoryToFindingAsync(string findingId, int? categoryId)
    {
        try
        {
            _logger.LogInformation("Assigning category {CategoryId} to finding {FindingId}", categoryId, findingId);

            var request = new AssignFindingCategoryModel { FindingCategoryId = categoryId };
            var response = await _httpClient.PutAsJsonAsync($"api/v1/findings/{findingId}/category", request);

            if (response.IsSuccessStatusCode)
            {
                return Result.Success();
            }
            else
            {
                _logger.LogWarning("Failed to assign category to finding. Status: {Status}", response.StatusCode);
                return Result.Failure($"Failed to assign category: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning category {CategoryId} to finding {FindingId}", categoryId, findingId);
            return Result.Failure($"Error assigning category: {ex.Message}");
        }
    }

    public async Task<Result<FindingCategoryModel?>> GetCategoryAsync(int categoryId)
    {
        try
        {
            _logger.LogInformation("Getting finding category {CategoryId}", categoryId);

            var response = await _httpClient.GetAsync($"api/v1/findings/categories/{categoryId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<FindingCategoryModel>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Success == true)
                {
                    return Result<FindingCategoryModel?>.Success(apiResponse.Data);
                }
                else
                {
                    _logger.LogWarning("API returned unsuccessful response: {Message}", apiResponse?.Message);
                    return Result<FindingCategoryModel?>.Failure(apiResponse?.Message ?? "Failed to get finding category");
                }
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return Result<FindingCategoryModel?>.Success(null);
            }
            else
            {
                _logger.LogWarning("Failed to get finding category. Status: {Status}", response.StatusCode);
                return Result<FindingCategoryModel?>.Failure($"Failed to get finding category: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting finding category: {CategoryId}", categoryId);
            return Result<FindingCategoryModel?>.Failure($"Error getting finding category: {ex.Message}");
        }
    }
}
