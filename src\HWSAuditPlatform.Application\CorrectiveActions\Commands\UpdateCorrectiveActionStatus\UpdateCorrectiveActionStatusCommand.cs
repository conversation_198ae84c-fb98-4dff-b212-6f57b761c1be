using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.CorrectiveActions.Commands.UpdateCorrectiveActionStatus;

/// <summary>
/// Command to update the status of a corrective action
/// </summary>
public class UpdateCorrectiveActionStatusCommand : BaseCommand<bool>
{
    /// <summary>
    /// The ID of the corrective action to update
    /// </summary>
    public string CorrectiveActionId { get; set; } = string.Empty;

    /// <summary>
    /// The new status for the corrective action
    /// </summary>
    public CorrectiveActionStatus Status { get; set; }

    /// <summary>
    /// Optional notes about the status change
    /// </summary>
    public string? StatusChangeNotes { get; set; }

    /// <summary>
    /// Completion date (required when marking as completed)
    /// </summary>
    public DateOnly? CompletionDate { get; set; }

    /// <summary>
    /// Verification date (required when marking as verified)
    /// </summary>
    public DateOnly? VerificationDate { get; set; }
}
