@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.WebAuditPWA.Services
@using HWSAuditPlatform.Domain.Enums

<div class="enhanced-findings-container">
    <!-- Enhanced Findings Input with Severity and Actions -->
    <EnhancedFindingDetailsInput
        Findings="DetailedFindings"
        FindingsChanged="OnDetailedFindingsChanged"
        ShowValidationSummary="ShowValidationSummary"
        EnableSeveritySelection="EnableSeveritySelection"
        EnableImmediateActions="EnableImmediateActions" />

    <!-- Finding Categorization (if enabled) -->
    @if (EnableCategorization && AuditTemplateId.HasValue)
    {
        <FindingCategorySelector
            AuditTemplateId="AuditTemplateId"
            SelectedCategoryId="SelectedCategoryId"
            SelectedCategoryIdChanged="OnCategoryChanged"
            ShowCategorySelection="HasFindings" />
    }

    <!-- Responsibility Information (if available) -->
    @if (ShowResponsibilityInfo && (HasResponsibleUser || HasRetrospectiveAnalyst))
    {
        <div class="responsibility-info">
            <div class="responsibility-header">
                <h6 class="responsibility-title">
                    <i class="fas fa-users me-2"></i>
                    Assigned Responsibilities
                </h6>
            </div>
            <div class="responsibility-content">
                @if (HasResponsibleUser)
                {
                    <div class="responsibility-item">
                        <div class="responsibility-role">
                            <i class="fas fa-user-check me-2"></i>
                            Corrective Actions
                        </div>
                        <div class="responsibility-user">@ResponsibleUserName</div>
                    </div>
                }
                @if (HasRetrospectiveAnalyst)
                {
                    <div class="responsibility-item">
                        <div class="responsibility-role">
                            <i class="fas fa-chart-line me-2"></i>
                            Retrospective Analysis
                        </div>
                        <div class="responsibility-user">@RetrospectiveAnalystUserName</div>
                    </div>
                }
            </div>
        </div>
    }
</div>

<style>
    .enhanced-findings-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .responsibility-info {
        background: linear-gradient(135deg, var(--industrial-gray) 0%, #252525 100%);
        border: 2px solid var(--industrial-border);
        border-radius: 12px;
        border-left: 4px solid var(--industrial-teal);
        padding: 1.25rem;
    }

    .responsibility-header {
        margin-bottom: 1rem;
    }

    .responsibility-title {
        display: flex;
        align-items: center;
        color: var(--industrial-teal);
        font-size: 1rem;
        font-weight: 700;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .responsibility-content {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .responsibility-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(20, 184, 166, 0.1);
        border: 1px solid var(--industrial-border-light);
        border-radius: 8px;
        padding: 0.75rem;
    }

    .responsibility-role {
        display: flex;
        align-items: center;
        color: var(--industrial-text);
        font-weight: 600;
        font-size: 0.9rem;
    }

    .responsibility-user {
        color: var(--industrial-text-muted);
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .responsibility-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .responsibility-role,
        .responsibility-user {
            font-size: 0.85rem;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .responsibility-info {
            padding: 1rem;
        }

        .responsibility-item {
            padding: 1rem;
        }
    }
</style>

@code {
    [Parameter] public List<string> Findings { get; set; } = new();
    [Parameter] public EventCallback<List<string>> FindingsChanged { get; set; }
    [Parameter] public EventCallback<List<DetailedFindingModel>> DetailedFindingsChanged { get; set; }
    [Parameter] public bool ShowValidationSummary { get; set; } = false;
    [Parameter] public bool EnableCategorization { get; set; } = false;
    [Parameter] public bool EnableSeveritySelection { get; set; } = true;
    [Parameter] public bool EnableImmediateActions { get; set; } = true;
    [Parameter] public int? AuditTemplateId { get; set; }
    [Parameter] public int? SelectedCategoryId { get; set; }
    [Parameter] public EventCallback<int?> SelectedCategoryIdChanged { get; set; }
    [Parameter] public bool ShowResponsibilityInfo { get; set; } = false;
    [Parameter] public string? ResponsibleUserName { get; set; }
    [Parameter] public string? RetrospectiveAnalystUserName { get; set; }

    private List<DetailedFindingModel> DetailedFindings { get; set; } = new();
    private bool HasFindings => DetailedFindings?.Any(f => !string.IsNullOrWhiteSpace(f.Description)) == true;
    private bool HasResponsibleUser => !string.IsNullOrEmpty(ResponsibleUserName);
    private bool HasRetrospectiveAnalyst => !string.IsNullOrEmpty(RetrospectiveAnalystUserName);

    protected override void OnParametersSet()
    {
        // Convert simple findings to detailed findings if needed
        if (Findings?.Any() == true && (!DetailedFindings?.Any() == true || DetailedFindings.Count != Findings.Count))
        {
            DetailedFindings = Findings.Select(f => new DetailedFindingModel
            {
                Description = f,
                SeverityLevel = SeverityLevel.Minor, // Default severity
                ImmediateAction = ""
            }).ToList();
        }
        else if (!DetailedFindings?.Any() == true)
        {
            DetailedFindings = new List<DetailedFindingModel>
            {
                new DetailedFindingModel
                {
                    Description = "",
                    SeverityLevel = SeverityLevel.Minor,
                    ImmediateAction = ""
                }
            };
        }
    }

    private async Task OnDetailedFindingsChanged(List<DetailedFindingModel> newDetailedFindings)
    {
        DetailedFindings = newDetailedFindings;

        // Store detailed findings data for enhanced submission
        if (DetailedFindingsChanged.HasDelegate)
        {
            await DetailedFindingsChanged.InvokeAsync(newDetailedFindings);
        }

        // Convert back to simple findings for backward compatibility
        var simpleFindings = newDetailedFindings.Select(f => f.Description).ToList();
        await FindingsChanged.InvokeAsync(simpleFindings);
    }

    private async Task OnCategoryChanged(int? categoryId)
    {
        await SelectedCategoryIdChanged.InvokeAsync(categoryId);
    }
}
