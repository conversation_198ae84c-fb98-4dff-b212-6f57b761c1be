using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Workflow;
using HWSAuditPlatform.Infrastructure.Persistence;

namespace HWSAuditPlatform.Infrastructure.Services;

/// <summary>
/// Service for creating and managing audit log entries
/// </summary>
public class AuditLogService : IAuditLogService
{
    private readonly ApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<AuditLogService> _logger;

    public AuditLogService(
        ApplicationDbContext context,
        ICurrentUserService currentUserService,
        IHttpContextAccessor httpContextAccessor,
        ILogger<AuditLogService> logger)
    {
        _context = context;
        _currentUserService = currentUserService;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public async Task<AuditLog> LogEntityCreatedAsync(
        string entityType,
        string entityId,
        string? newValues = null,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default)
    {
        return await CreateAuditLogAsync(
            "Create",
            entityType,
            entityId,
            oldValues: null,
            newValues: newValues,
            details: details,
            userId: userId,
            eventTimestamp: eventTimestamp,
            cancellationToken: cancellationToken);
    }

    public async Task<AuditLog> LogEntityUpdatedAsync(
        string entityType,
        string entityId,
        string? oldValues = null,
        string? newValues = null,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default)
    {
        return await CreateAuditLogAsync(
            "Update",
            entityType,
            entityId,
            oldValues: oldValues,
            newValues: newValues,
            details: details,
            userId: userId,
            eventTimestamp: eventTimestamp,
            cancellationToken: cancellationToken);
    }

    public async Task<AuditLog> LogEntityDeletedAsync(
        string entityType,
        string entityId,
        string? oldValues = null,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default)
    {
        return await CreateAuditLogAsync(
            "Delete",
            entityType,
            entityId,
            oldValues: oldValues,
            newValues: null,
            details: details,
            userId: userId,
            eventTimestamp: eventTimestamp,
            cancellationToken: cancellationToken);
    }

    public async Task<AuditLog> LogBusinessOperationAsync(
        string actionType,
        string entityType,
        string entityId,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default)
    {
        return await CreateAuditLogAsync(
            actionType,
            entityType,
            entityId,
            oldValues: null,
            newValues: null,
            details: details,
            userId: userId,
            eventTimestamp: eventTimestamp,
            cancellationToken: cancellationToken);
    }

    public async Task<AuditLog> LogAuthenticationEventAsync(
        string actionType,
        string username,
        string? details = null,
        string? userId = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default)
    {
        return await CreateAuditLogAsync(
            actionType,
            "Authentication",
            username,
            oldValues: null,
            newValues: null,
            details: details,
            userId: userId,
            eventTimestamp: eventTimestamp,
            cancellationToken: cancellationToken);
    }

    public async Task<AuditLog> LogSystemEventAsync(
        string actionType,
        string? details = null,
        DateTime? eventTimestamp = null,
        CancellationToken cancellationToken = default)
    {
        return await CreateAuditLogAsync(
            actionType,
            "System",
            "System",
            oldValues: null,
            newValues: null,
            details: details,
            userId: null, // System events have no user
            eventTimestamp: eventTimestamp,
            cancellationToken: cancellationToken);
    }

    private async Task<AuditLog> CreateAuditLogAsync(
        string actionType,
        string entityType,
        string entityId,
        string? oldValues,
        string? newValues,
        string? details,
        string? userId,
        DateTime? eventTimestamp,
        CancellationToken cancellationToken)
    {
        try
        {
            var auditLog = new AuditLog
            {
                ActionType = actionType,
                EntityType = entityType,
                EntityId = entityId,
                OldValues = oldValues,
                NewValues = newValues,
                Details = details,
                UserId = userId ?? _currentUserService.UserId ?? "System",
                EventTimestamp = eventTimestamp ?? DateTime.UtcNow,
                ServerReceivedAt = DateTime.UtcNow,
                IPAddress = GetClientIPAddress(),
                AppVersion = GetAppVersion()
            };

            _context.AuditLogs.Add(auditLog);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Audit log created: {ActionType} on {EntityType} {EntityId} by user {UserId}",
                actionType, entityType, entityId, auditLog.UserId ?? "System");

            return auditLog;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating audit log for {ActionType} on {EntityType} {EntityId}",
                actionType, entityType, entityId);

            // Return a default audit log to avoid breaking the application
            return new AuditLog
            {
                ActionType = actionType,
                EntityType = entityType,
                EntityId = entityId,
                OldValues = oldValues,
                NewValues = newValues,
                Details = details,
                UserId = userId ?? _currentUserService.UserId ?? "System",
                EventTimestamp = eventTimestamp ?? DateTime.UtcNow,
                ServerReceivedAt = DateTime.UtcNow,
                IPAddress = null,
                AppVersion = null
            };
        }
    }

    private string? GetClientIPAddress()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null) return null;

            // Check for forwarded IP first (in case of proxy/load balancer)
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            // Check for real IP header
            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            // Fall back to connection remote IP
            return httpContext.Connection.RemoteIpAddress?.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get client IP address for audit log");
            return null;
        }
    }

    private string? GetAppVersion()
    {
        try
        {
            var assembly = System.Reflection.Assembly.GetEntryAssembly();
            return assembly?.GetName().Version?.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get app version for audit log");
            return null;
        }
    }
}
