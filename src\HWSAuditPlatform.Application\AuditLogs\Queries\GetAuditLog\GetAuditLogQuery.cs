using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.AuditLogs.DTOs;

namespace HWSAuditPlatform.Application.AuditLogs.Queries.GetAuditLog;

/// <summary>
/// Query to get a specific audit log by ID
/// </summary>
public class GetAuditLogQuery : BaseQuery<AuditLogDto?>
{
    /// <summary>
    /// ID of the audit log to retrieve
    /// </summary>
    public int Id { get; set; }
}
