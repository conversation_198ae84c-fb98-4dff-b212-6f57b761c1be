using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Workflow;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Infrastructure.Interceptors;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Tests.Infrastructure.Interceptors;

public class AuditLogInterceptorTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;
    private readonly Mock<ILogger<AuditLogInterceptor>> _loggerMock;

    public AuditLogInterceptorTests()
    {
        // Setup mocks
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        _loggerMock = new Mock<ILogger<AuditLogInterceptor>>();

        // Setup HTTP context with user claims
        var httpContext = new DefaultHttpContext();
        httpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new[]
        {
            new Claim("sub", "test-user-guid-12345678901234567890"),
            new Claim(ClaimTypes.Name, "testuser")
        }));
        httpContext.Connection.RemoteIpAddress = System.Net.IPAddress.Parse("*************");

        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContext);

        // Setup in-memory database with interceptor
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .AddInterceptors(new AuditLogInterceptor(new TestCurrentUserService(), _loggerMock.Object))
            .Options;
        
        _context = new ApplicationDbContext(options, new TestCurrentUserService());
    }

    [Fact]
    public async Task SaveChangesAsync_WhenEntityCreated_ShouldCreateAuditLogEntry()
    {
        // Arrange
        var user = User.Create(
            username: "newuser",
            firstName: "New",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: "new-user-guid-123456789012345678901234567890",
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        // Act
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Assert
        var auditLog = await _context.AuditLogs
            .Where(al => al.EntityType == "User" && al.ActionType == "Create")
            .FirstOrDefaultAsync();

        Assert.NotNull(auditLog);
        Assert.Equal("User", auditLog.EntityType);
        Assert.Equal("Create", auditLog.ActionType);
        Assert.Equal(user.Id, auditLog.EntityId);
        Assert.Equal("test-user-id", auditLog.UserId);
        Assert.Contains("newuser", auditLog.NewValues);
        Assert.Contains("New", auditLog.NewValues);
        Assert.Null(auditLog.OldValues);
        Assert.Null(auditLog.IPAddress); // Should be null when no HTTP context
    }

    [Fact]
    public async Task SaveChangesAsync_WhenEntityUpdated_ShouldCreateAuditLogEntry()
    {
        // Arrange - Create initial user
        var user = User.Create(
            username: "updateuser",
            firstName: "Original",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: "update-user-guid-123456789012345678901234567890",
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Clear any audit logs from the creation
        var creationLogs = _context.AuditLogs.Where(al => al.ActionType == "Create");
        _context.AuditLogs.RemoveRange(creationLogs);
        await _context.SaveChangesAsync();

        // Act - Update the user
        user.FirstName = "Updated";
        user.Email = "<EMAIL>";
        await _context.SaveChangesAsync();

        // Assert
        var auditLog = await _context.AuditLogs
            .Where(al => al.EntityType == "User" && al.ActionType == "Update")
            .FirstOrDefaultAsync();

        Assert.NotNull(auditLog);
        Assert.Equal("User", auditLog.EntityType);
        Assert.Equal("Update", auditLog.ActionType);
        Assert.Equal(user.Id, auditLog.EntityId);
        Assert.Contains("Original", auditLog.OldValues);
        Assert.Contains("<EMAIL>", auditLog.OldValues);
        Assert.Contains("Updated", auditLog.NewValues);
        Assert.Contains("<EMAIL>", auditLog.NewValues);
    }

    [Fact]
    public async Task SaveChangesAsync_WhenEntityDeleted_ShouldCreateAuditLogEntry()
    {
        // Arrange - Create initial user
        var user = User.Create(
            username: "deleteuser",
            firstName: "Delete",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: "delete-user-guid-123456789012345678901234567890",
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Clear any audit logs from the creation
        var creationLogs = _context.AuditLogs.Where(al => al.ActionType == "Create");
        _context.AuditLogs.RemoveRange(creationLogs);
        await _context.SaveChangesAsync();

        // Act - Delete the user
        _context.Users.Remove(user);
        await _context.SaveChangesAsync();

        // Assert
        var auditLog = await _context.AuditLogs
            .Where(al => al.EntityType == "User" && al.ActionType == "Delete")
            .FirstOrDefaultAsync();

        Assert.NotNull(auditLog);
        Assert.Equal("User", auditLog.EntityType);
        Assert.Equal("Delete", auditLog.ActionType);
        Assert.Equal(user.Id, auditLog.EntityId);
        Assert.Contains("deleteuser", auditLog.OldValues);
        Assert.Contains("Delete", auditLog.OldValues);
        Assert.Null(auditLog.NewValues);
    }

    [Fact]
    public async Task SaveChangesAsync_WhenAuditLogEntityChanged_ShouldNotCreateAuditLogEntry()
    {
        // Arrange
        var auditLog = new AuditLog
        {
            EventTimestamp = DateTime.UtcNow,
            ServerReceivedAt = DateTime.UtcNow,
            UserId = "test-user-id",
            EntityType = "TestEntity",
            EntityId = "test-entity-id",
            ActionType = "TestAction"
        };

        // Act
        _context.AuditLogs.Add(auditLog);
        await _context.SaveChangesAsync();

        // Assert - Should only have the one audit log we added, not an additional one for the audit log creation
        var auditLogCount = await _context.AuditLogs.CountAsync();
        Assert.Equal(1, auditLogCount);
    }

    [Fact]
    public async Task SaveChangesAsync_WithoutHttpContext_ShouldStillCreateAuditLogEntry()
    {
        // Arrange - Create a separate context with null user service
        var nullUserServiceMock = new Mock<ICurrentUserService>();
        nullUserServiceMock.Setup(x => x.UserId).Returns((string?)null);
        nullUserServiceMock.Setup(x => x.Username).Returns((string?)null);
        nullUserServiceMock.Setup(x => x.IsAuthenticated).Returns(false);

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .AddInterceptors(new AuditLogInterceptor(nullUserServiceMock.Object, _loggerMock.Object))
            .Options;

        using var contextWithNullUser = new ApplicationDbContext(options, nullUserServiceMock.Object);

        var user = User.Create(
            username: "nocontextuser",
            firstName: "No",
            lastName: "Context",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: "no-context-user-guid-123456789012345678901234567890",
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        // Act
        contextWithNullUser.Users.Add(user);
        await contextWithNullUser.SaveChangesAsync();

        // Assert
        var auditLog = await contextWithNullUser.AuditLogs
            .Where(al => al.EntityType == "User" && al.ActionType == "Create")
            .FirstOrDefaultAsync();

        Assert.NotNull(auditLog);
        Assert.Equal("System", auditLog.UserId); // Should default to "System"
        Assert.Null(auditLog.IPAddress); // Should be null when no HTTP context
    }

    [Fact]
    public async Task SaveChangesAsync_WithMultipleChanges_ShouldCreateMultipleAuditLogEntries()
    {
        // Arrange
        var user1 = User.Create(
            username: "multiuser1",
            firstName: "Multi1",
            lastName: "User1",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: "multi-user1-guid-123456789012345678901234567890",
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        var user2 = User.Create(
            username: "multiuser2",
            firstName: "Multi2",
            lastName: "User2",
            email: "<EMAIL>",
            roleId: 2,
            factoryId: null,
            isActive: true,
            adObjectGuid: "multi-user2-guid-123456789012345678901234567890",
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        // Act
        _context.Users.AddRange(user1, user2);
        await _context.SaveChangesAsync();

        // Assert
        var auditLogs = await _context.AuditLogs
            .Where(al => al.EntityType == "User" && al.ActionType == "Create")
            .ToListAsync();

        Assert.Equal(2, auditLogs.Count);
        Assert.Contains(auditLogs, al => al.EntityId == user1.Id);
        Assert.Contains(auditLogs, al => al.EntityId == user2.Id);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
