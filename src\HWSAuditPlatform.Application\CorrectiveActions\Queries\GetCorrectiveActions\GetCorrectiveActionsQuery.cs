using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.CorrectiveActions.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.CorrectiveActions.Queries.GetCorrectiveActions;

/// <summary>
/// Query to get corrective actions with filtering and pagination
/// </summary>
public class GetCorrectiveActionsQuery : BaseQuery<PagedResult<CorrectiveActionSummaryDto>>
{
    /// <summary>
    /// Filter by finding ID
    /// </summary>
    public string? FindingId { get; set; }

    /// <summary>
    /// Filter by assigned to user ID
    /// </summary>
    public string? AssignedToUserId { get; set; }

    /// <summary>
    /// Filter by assigned by user ID
    /// </summary>
    public string? AssignedByUserId { get; set; }

    /// <summary>
    /// Filter by corrective action status
    /// </summary>
    public CorrectiveActionStatus? Status { get; set; }

    /// <summary>
    /// Filter by due date range - from
    /// </summary>
    public DateOnly? DueDateFrom { get; set; }

    /// <summary>
    /// Filter by due date range - to
    /// </summary>
    public DateOnly? DueDateTo { get; set; }

    /// <summary>
    /// Filter by completion date range - from
    /// </summary>
    public DateOnly? CompletionDateFrom { get; set; }

    /// <summary>
    /// Filter by completion date range - to
    /// </summary>
    public DateOnly? CompletionDateTo { get; set; }

    /// <summary>
    /// Show only overdue corrective actions
    /// </summary>
    public bool? IsOverdue { get; set; }

    /// <summary>
    /// Show only completed corrective actions
    /// </summary>
    public bool? IsCompleted { get; set; }

    /// <summary>
    /// Show only in-progress corrective actions
    /// </summary>
    public bool? IsInProgress { get; set; }

    /// <summary>
    /// Search term for action description
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "DueDate";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "asc";
}
