using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Workflow;

namespace HWSAuditPlatform.Infrastructure.Interceptors;

/// <summary>
/// EF Core interceptor that automatically creates audit log entries for entity changes
/// </summary>
public class AuditLogInterceptor : SaveChangesInterceptor
{
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<AuditLogInterceptor> _logger;

    public AuditLogInterceptor(
        ICurrentUserService currentUserService,
        ILogger<AuditLogInterceptor> logger)
    {
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result)
    {
        CreateAuditLogs(eventData.Context);
        return base.SavingChanges(eventData, result);
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        CreateAuditLogs(eventData.Context);
        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private void CreateAuditLogs(DbContext? context)
    {
        if (context == null) return;

        try
        {
            var auditLogs = new List<AuditLog>();
            var currentUserId = _currentUserService.UserId ?? "System";
            var now = DateTime.UtcNow;

            var entries = context.ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added || 
                           e.State == EntityState.Modified || 
                           e.State == EntityState.Deleted)
                .Where(e => e.Entity is not AuditLog) // Don't audit the audit logs themselves
                .ToList();

            foreach (var entry in entries)
            {
                var entityType = entry.Entity.GetType().Name;
                var entityId = GetEntityId(entry);

                if (string.IsNullOrEmpty(entityId))
                {
                    _logger.LogWarning("Could not determine entity ID for {EntityType}", entityType);
                    continue;
                }

                var auditLog = new AuditLog
                {
                    EntityType = entityType,
                    EntityId = entityId,
                    UserId = currentUserId,
                    EventTimestamp = now,
                    ServerReceivedAt = now
                };

                switch (entry.State)
                {
                    case EntityState.Added:
                        auditLog.ActionType = "Create";
                        auditLog.NewValues = SerializeEntity(entry, EntityState.Added);
                        break;

                    case EntityState.Modified:
                        auditLog.ActionType = "Update";
                        auditLog.OldValues = SerializeEntity(entry, EntityState.Modified, useOriginalValues: true);
                        auditLog.NewValues = SerializeEntity(entry, EntityState.Modified, useOriginalValues: false);
                        break;

                    case EntityState.Deleted:
                        auditLog.ActionType = "Delete";
                        auditLog.OldValues = SerializeEntity(entry, EntityState.Deleted);
                        break;
                }

                auditLogs.Add(auditLog);
            }

            // Add audit logs to the context
            if (auditLogs.Any())
            {
                context.Set<AuditLog>().AddRange(auditLogs);
                _logger.LogDebug("Created {Count} audit log entries", auditLogs.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating audit logs during SaveChanges");
            // Don't throw - we don't want audit logging to break the main operation
        }
    }

    private string GetEntityId(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry)
    {
        try
        {
            // Try to get the primary key value
            var keyValues = entry.Metadata.FindPrimaryKey()?.Properties
                .Select(p => entry.Property(p.Name).CurrentValue?.ToString())
                .Where(v => !string.IsNullOrEmpty(v))
                .ToList();

            if (keyValues?.Any() == true)
            {
                return string.Join(",", keyValues);
            }

            // Fallback: try common ID property names
            var idProperty = entry.Properties.FirstOrDefault(p => 
                p.Metadata.Name.Equals("Id", StringComparison.OrdinalIgnoreCase) ||
                p.Metadata.Name.EndsWith("Id", StringComparison.OrdinalIgnoreCase));

            return idProperty?.CurrentValue?.ToString() ?? "Unknown";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting entity ID for {EntityType}", entry.Entity.GetType().Name);
            return "Unknown";
        }
    }

    private string? SerializeEntity(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry, EntityState state, bool useOriginalValues = false)
    {
        try
        {
            var properties = new Dictionary<string, object?>();

            foreach (var property in entry.Properties)
            {
                // Skip navigation properties and computed properties
                if (property.Metadata.IsForeignKey() || 
                    property.Metadata.IsKey() ||
                    property.Metadata.ValueGenerated != Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.Never)
                {
                    continue;
                }

                var propertyName = property.Metadata.Name;
                object? value = null;

                try
                {
                    if (state == EntityState.Added)
                    {
                        value = property.CurrentValue;
                    }
                    else if (state == EntityState.Modified)
                    {
                        value = useOriginalValues ? property.OriginalValue : property.CurrentValue;
                    }
                    else if (state == EntityState.Deleted)
                    {
                        value = property.OriginalValue;
                    }

                    // Handle special types that might not serialize well
                    if (value != null)
                    {
                        var valueType = value.GetType();
                        if (valueType == typeof(DateTime) || valueType == typeof(DateTime?))
                        {
                            value = ((DateTime)value).ToString("O"); // ISO 8601 format
                        }
                        else if (valueType.IsEnum)
                        {
                            value = value.ToString();
                        }
                    }

                    properties[propertyName] = value;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error getting property value for {PropertyName} on {EntityType}", 
                        propertyName, entry.Entity.GetType().Name);
                    properties[propertyName] = "<Error getting value>";
                }
            }

            if (!properties.Any())
                return null;

            return JsonSerializer.Serialize(properties, new JsonSerializerOptions
            {
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error serializing entity {EntityType} for audit log", entry.Entity.GetType().Name);
            return $"<Serialization error: {ex.Message}>";
        }
    }
}
