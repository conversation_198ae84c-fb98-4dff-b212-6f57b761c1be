using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Findings.DTOs;

namespace HWSAuditPlatform.Application.Findings.Commands.CreateFindingFromAuditAnswer;

/// <summary>
/// Command to automatically create a finding from an audit answer that indicates a failure
/// </summary>
public class CreateFindingFromAuditAnswerCommand : BaseCommand<FindingDto?>
{
    /// <summary>
    /// The audit answer ID that generated this finding
    /// </summary>
    public string AuditAnswerId { get; set; } = string.Empty;

    /// <summary>
    /// Whether to force creation even if no failure indicators are present
    /// </summary>
    public bool ForceCreation { get; set; } = false;
}
