using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.CorrectiveActions.DTOs;

/// <summary>
/// Complete DTO for corrective action details
/// </summary>
public class CorrectiveActionDto
{
    /// <summary>
    /// Unique identifier for the corrective action
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// ID of the finding this corrective action addresses
    /// </summary>
    public string FindingId { get; set; } = string.Empty;

    /// <summary>
    /// Description of the corrective action to be taken
    /// </summary>
    public string ActionDescription { get; set; } = string.Empty;

    /// <summary>
    /// User ID of the person assigned to complete this action
    /// </summary>
    public string AssignedToUserId { get; set; } = string.Empty;

    /// <summary>
    /// Username of the assigned user
    /// </summary>
    public string? AssignedToUserName { get; set; }

    /// <summary>
    /// Full name of the assigned user
    /// </summary>
    public string? AssignedToUserFullName { get; set; }

    /// <summary>
    /// User ID of the person who assigned this action
    /// </summary>
    public string AssignedByUserId { get; set; } = string.Empty;

    /// <summary>
    /// Username of the user who assigned this action
    /// </summary>
    public string? AssignedByUserName { get; set; }

    /// <summary>
    /// Full name of the user who assigned this action
    /// </summary>
    public string? AssignedByUserFullName { get; set; }

    /// <summary>
    /// Due date for completing this corrective action
    /// </summary>
    public DateOnly DueDate { get; set; }

    /// <summary>
    /// Date when the action was completed
    /// </summary>
    public DateOnly? CompletionDate { get; set; }

    /// <summary>
    /// Date when the completion was verified
    /// </summary>
    public DateOnly? VerificationDate { get; set; }

    /// <summary>
    /// User ID of the person who verified the completion
    /// </summary>
    public string? VerifiedByUserId { get; set; }

    /// <summary>
    /// Username of the user who verified the completion
    /// </summary>
    public string? VerifiedByUserName { get; set; }

    /// <summary>
    /// Full name of the user who verified the completion
    /// </summary>
    public string? VerifiedByUserFullName { get; set; }

    /// <summary>
    /// Current status of the corrective action
    /// </summary>
    public CorrectiveActionStatus Status { get; set; }

    /// <summary>
    /// Notes about the assignment
    /// </summary>
    public string? AssignmentNotes { get; set; }

    /// <summary>
    /// Notes about the completion
    /// </summary>
    public string? CompletionNotes { get; set; }

    /// <summary>
    /// Notes about the verification
    /// </summary>
    public string? VerificationNotes { get; set; }

    /// <summary>
    /// Whether this corrective action is overdue
    /// </summary>
    public bool IsOverdue { get; set; }

    /// <summary>
    /// Whether this corrective action is completed
    /// </summary>
    public bool IsCompleted { get; set; }

    /// <summary>
    /// Whether this corrective action is in progress
    /// </summary>
    public bool IsInProgress { get; set; }

    /// <summary>
    /// When the corrective action was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the corrective action was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Who created the corrective action
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Who last updated the corrective action
    /// </summary>
    public string? UpdatedBy { get; set; }
}
