@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Entities.Audits
@using HWSAuditPlatform.Domain.Enums
@inject IAuditApiService AuditApiService
@inject ILogger<AuditPendingReviewComponent> <PERSON><PERSON>
@inject NavigationManager Navigation

<div class="audit-pending-review">
    <div class="page-header">
        <h1>Pending Review</h1>
        <p class="text-muted">Audits awaiting manager review and approval</p>
    </div>

    <HWSAuditPlatform.WebApp.Components.Shared.LoadingComponent 
        IsLoading="@isLoading" 
        Message="Loading pending audits..." />

    @if (!isLoading)
    {
        @if (pendingAudits?.Any() == true)
        {
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                <strong>@pendingAudits.Count() audit(s) pending review</strong> - These audits are completed and awaiting manager approval.
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Audit Template</th>
                                    <th>Location</th>
                                    <th>Completed Date</th>
                                    <th>Score</th>
                                    <th>Auditor</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var audit in pendingAudits.OrderBy(a => a.CompletedAt))
                                {
                                    <tr class="table-warning">
                                        <td>
                                            <div class="fw-semibold">@(audit.AuditTemplate?.TemplateName ?? "Unknown Template")</div>
                                        </td>
                                        <td>
                                            <div>@(audit.Factory?.FactoryName ?? "Unknown Factory")</div>
                                            @if (!string.IsNullOrEmpty(audit.Area?.AreaName))
                                            {
                                                <small class="text-muted">@audit.Area.AreaName</small>
                                            }
                                        </td>
                                        <td>
                                            @if (audit.CompletedAt.HasValue)
                                            {
                                                @audit.CompletedAt.Value.ToString("MMM dd, yyyy")
                                            }
                                            else
                                            {
                                                <span class="text-muted">Not completed</span>
                                            }
                                        </td>
                                        <td>
                                            @if (audit.OverallScore.HasValue)
                                            {
                                                <div class="badge-container">
                                                    <span class="badge badge-sm @GetScoreBadgeClass(audit.OverallScore.Value)" title="@audit.OverallScore.Value.ToString("F1")%">
                                                        @audit.OverallScore.Value.ToString("F1")%
                                                    </span>
                                                </div>
                                            }
                                            else
                                            {
                                                <span class="text-muted">No score</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="text-muted">Auditor info not available</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" @onclick="() => ViewAudit(audit.Id)"
                                                        title="View audit details">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" @onclick="() => ReviewAudit(audit.Id)"
                                                        title="Review and approve">
                                                    <i class="bi bi-check-circle"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-clipboard-check text-success" style="font-size: 4rem;"></i>
                    <h3 class="mt-3 text-success">All Caught Up!</h3>
                    <p class="text-muted">No audits are currently pending review. All completed audits have been processed.</p>
                </div>
            </div>
        }
    }
</div>

<style>
    .audit-pending-review {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 30px;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
</style>

@code {
    private bool isLoading = true;
    private IEnumerable<Audit>? pendingAudits;

    protected override async Task OnInitializedAsync()
    {
        await LoadPendingAudits();
    }

    private async Task LoadPendingAudits()
    {
        try
        {
            isLoading = true;
            
            var allAudits = await AuditApiService.GetAuditsAsync();
            if (allAudits != null)
            {
                pendingAudits = allAudits.Where(a => a.OverallStatus == AuditOverallStatus.PendingManagerReview);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading pending audits");
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetScoreBadgeClass(decimal score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-primary",
            >= 70 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private void ViewAudit(string auditId)
    {
        Navigation.NavigateTo($"/audits/{auditId}");
    }

    private void ReviewAudit(string auditId)
    {
        Navigation.NavigateTo($"/audits/{auditId}/review");
    }
}
