using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Findings.DTOs;

/// <summary>
/// Summary DTO for findings in list views
/// </summary>
public class FindingSummaryDto
{
    /// <summary>
    /// Unique identifier for the finding
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Auto-generated finding code
    /// </summary>
    public string? FindingCode { get; set; }

    /// <summary>
    /// Description of the non-conformity
    /// </summary>
    public string FindingDescription { get; set; } = string.Empty;

    /// <summary>
    /// Severity level of the finding
    /// </summary>
    public SeverityLevel FindingSeverityLevel { get; set; }

    /// <summary>
    /// Current status of the finding
    /// </summary>
    public FindingStatus Status { get; set; }

    /// <summary>
    /// Target date for resolving this finding
    /// </summary>
    public DateOnly? DueDate { get; set; }

    /// <summary>
    /// Full name of the responsible user
    /// </summary>
    public string? ResponsibleUserName { get; set; }

    /// <summary>
    /// Name of the finding category
    /// </summary>
    public string? FindingCategoryName { get; set; }

    /// <summary>
    /// Color code for the finding category
    /// </summary>
    public string? FindingCategoryColorCode { get; set; }

    /// <summary>
    /// Name of the area where the finding was identified
    /// </summary>
    public string AreaName { get; set; } = string.Empty;

    /// <summary>
    /// Name of the factory where the finding was identified
    /// </summary>
    public string FactoryName { get; set; } = string.Empty;

    /// <summary>
    /// Total number of corrective actions for this finding
    /// </summary>
    public int CorrectiveActionCount { get; set; }

    /// <summary>
    /// Number of open corrective actions for this finding
    /// </summary>
    public int OpenCorrectiveActionCount { get; set; }

    /// <summary>
    /// Whether this finding is overdue
    /// </summary>
    public bool IsOverdue { get; set; }

    /// <summary>
    /// Whether this finding is open
    /// </summary>
    public bool IsOpen { get; set; }

    /// <summary>
    /// When the finding was created
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
