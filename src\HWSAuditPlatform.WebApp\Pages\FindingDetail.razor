@page "/findings/{FindingId}"
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.Domain.Enums
@inject IFindingApiService FindingApiService
@inject IFindingCategoryApiService FindingCategoryApiService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@attribute [Authorize]

<PageTitle>Finding Details - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading finding details...</p>
        </div>
    }
    else if (finding == null)
    {
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h4>Finding Not Found</h4>
            <p class="text-muted">The requested finding could not be found.</p>
            <button class="btn btn-primary" @onclick="NavigateToFindings">
                <i class="fas fa-arrow-left"></i> Back to Findings
            </button>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="/findings" class="text-decoration-none">Findings</a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    @(finding.FindingCode ?? $"Finding #{finding.Id.Substring(0, 8)}")
                                </li>
                            </ol>
                        </nav>
                        <h2 class="mb-1">Finding Details</h2>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" @onclick="RefreshData">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i> Actions
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" @onclick="() => ShowStatusModal()">
                                    <i class="fas fa-edit me-2"></i>Update Status
                                </a></li>
                                <li><a class="dropdown-item" href="#" @onclick="() => ShowCategoryModal()">
                                    <i class="fas fa-tags me-2"></i>Assign Category
                                </a></li>
                                <li><a class="dropdown-item" href="#" @onclick="() => ShowResponsibilityModal()">
                                    <i class="fas fa-user-check me-2"></i>Assign Responsibility
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" @onclick="() => ViewAudit()">
                                    <i class="fas fa-clipboard-check me-2"></i>View Related Audit
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Finding Overview Card -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                @if (!string.IsNullOrEmpty(finding.FindingCode))
                                {
                                    <span class="text-muted me-2">@finding.FindingCode</span>
                                }
                                Finding Overview
                            </h5>
                            <div class="d-flex gap-2">
                                <span class="badge @GetSeverityBadgeClass() fs-6">@finding.FindingSeverityLevel</span>
                                <span class="badge @GetStatusBadgeClass() fs-6">@finding.Status</span>
                                @if (finding.IsOverdue)
                                {
                                    <span class="badge bg-danger fs-6">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Overdue
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="text-muted mb-2">Description</h6>
                                <p class="mb-3">@finding.FindingDescription</p>
                                
                                @if (!string.IsNullOrEmpty(finding.FindingCategoryName))
                                {
                                    <h6 class="text-muted mb-2">Category</h6>
                                    <div class="mb-3">
                                        <span class="badge bg-info fs-6">
                                            @if (!string.IsNullOrEmpty(finding.FindingCategoryIconName))
                                            {
                                                <i class="@GetIconClass(finding.FindingCategoryIconName) me-1"></i>
                                            }
                                            @finding.FindingCategoryName
                                        </span>
                                    </div>
                                }
                            </div>
                            <div class="col-md-4">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-1">Audit Information</h6>
                                        <p class="mb-1"><strong>Audit:</strong> @finding.AuditTitle</p>
                                        <p class="mb-1"><strong>Area:</strong> @finding.AreaName</p>
                                        <p class="mb-0"><strong>Factory:</strong> @finding.FactoryName</p>
                                    </div>
                                    
                                    @if (finding.DueDate.HasValue)
                                    {
                                        <div class="col-12">
                                            <h6 class="text-muted mb-1">Due Date</h6>
                                            <p class="mb-0 @(finding.IsOverdue ? "text-danger fw-bold" : "")">
                                                @finding.DueDate.Value.ToString("MMM dd, yyyy")
                                                @if (finding.IsOverdue)
                                                {
                                                    <i class="fas fa-exclamation-triangle ms-1"></i>
                                                }
                                            </p>
                                        </div>
                                    }
                                    
                                    <div class="col-12">
                                        <h6 class="text-muted mb-1">Created</h6>
                                        <p class="mb-0">@finding.CreatedAt.ToString("MMM dd, yyyy HH:mm")</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Responsibility Card -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="card-title mb-0">Responsibility Assignment</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted mb-2">Responsible Person</h6>
                                @if (finding.HasResponsibleUser)
                                {
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-check text-success me-2"></i>
                                        <span>@finding.ResponsibleUserFullName</span>
                                    </div>
                                }
                                else
                                {
                                    <div class="d-flex align-items-center text-muted">
                                        <i class="fas fa-user-slash me-2"></i>
                                        <span>Not assigned</span>
                                    </div>
                                }
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted mb-2">Retrospective Analyst</h6>
                                @if (finding.HasRetrospectiveAnalyst)
                                {
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-chart-line text-info me-2"></i>
                                        <span>@finding.RetrospectiveAnalystUserFullName</span>
                                    </div>
                                }
                                else
                                {
                                    <div class="d-flex align-items-center text-muted">
                                        <i class="fas fa-chart-line me-2"></i>
                                        <span>Not assigned</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Corrective Actions Card -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Corrective Actions</h5>
                            <button class="btn btn-sm btn-primary" @onclick="CreateCorrectiveAction">
                                <i class="fas fa-plus"></i> Add Action
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (finding.CorrectiveActionCount > 0)
                        {
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                This finding has @finding.CorrectiveActionCount corrective action(s) 
                                (@finding.OpenCorrectiveActionCount still open).
                            </div>
                            <button class="btn btn-outline-primary" @onclick="ViewCorrectiveActions">
                                <i class="fas fa-tasks me-2"></i>View All Corrective Actions
                            </button>
                        }
                        else
                        {
                            <div class="text-center py-3">
                                <i class="fas fa-tasks fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">No corrective actions have been created for this finding yet.</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public string FindingId { get; set; } = string.Empty;

    private bool isLoading = true;
    private FindingDto? finding;

    protected override async Task OnInitializedAsync()
    {
        await LoadFinding();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(FindingId))
        {
            await LoadFinding();
        }
    }

    private async Task LoadFinding()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            finding = await FindingApiService.GetFindingAsync(FindingId, includeCorrectiveActions: true);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", "Error loading finding:", ex.Message);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadFinding();
    }

    private void ShowStatusModal()
    {
        // TODO: Implement status update modal
        JSRuntime.InvokeVoidAsync("alert", "Status update functionality will be implemented soon.");
    }

    private void ShowCategoryModal()
    {
        // TODO: Implement category assignment modal
        JSRuntime.InvokeVoidAsync("alert", "Category assignment functionality will be implemented soon.");
    }

    private void ShowResponsibilityModal()
    {
        // TODO: Implement responsibility assignment modal
        JSRuntime.InvokeVoidAsync("alert", "Responsibility assignment functionality will be implemented soon.");
    }

    private void ViewAudit()
    {
        if (finding != null)
        {
            Navigation.NavigateTo($"/audits/{finding.AuditId}");
        }
    }

    private void CreateCorrectiveAction()
    {
        // TODO: Implement create corrective action functionality
        JSRuntime.InvokeVoidAsync("alert", "Create corrective action functionality will be implemented soon.");
    }

    private void ViewCorrectiveActions()
    {
        if (finding != null)
        {
            Navigation.NavigateTo($"/corrective-actions?findingId={finding.Id}");
        }
    }

    private string GetSeverityBadgeClass()
    {
        if (finding == null) return "bg-light";
        
        return finding.FindingSeverityLevel.ToString().ToLower() switch
        {
            "critical" => "bg-danger",
            "major" => "bg-warning",
            "minor" => "bg-info",
            "observation" => "bg-secondary",
            _ => "bg-light"
        };
    }

    private string GetStatusBadgeClass()
    {
        if (finding == null) return "bg-light";
        return finding.IsOpen ? "bg-warning" : "bg-success";
    }

    private static string GetIconClass(string iconName)
    {
        return iconName switch
        {
            "warning-triangle" => "fas fa-exclamation-triangle",
            "exclamation-circle" => "fas fa-exclamation-circle",
            "info-circle" => "fas fa-info-circle",
            "shield-alt" => "fas fa-shield-alt",
            "tools" => "fas fa-tools",
            "cog" => "fas fa-cog",
            "bug" => "fas fa-bug",
            "fire" => "fas fa-fire",
            "bolt" => "fas fa-bolt",
            "eye" => "fas fa-eye",
            _ => "fas fa-tag"
        };
    }

    private void NavigateToFindings()
    {
        Navigation.NavigateTo("/findings");
    }
}
