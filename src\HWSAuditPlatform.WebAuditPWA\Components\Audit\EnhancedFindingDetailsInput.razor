@using HWSAuditPlatform.WebAuditPWA.Models
@using HWSAuditPlatform.Domain.Enums

<div class="enhanced-finding-details-container">
    <div class="findings-header">
        <h4 class="findings-title">
            <i class="fas fa-exclamation-triangle"></i>
            Findings Required
        </h4>
        <p class="findings-subtitle">Provide detailed findings with severity levels and immediate actions</p>
    </div>

    <div class="findings-list">
        @if (Findings?.Any() == true)
        {
            @for (int i = 0; i < Findings.Count; i++)
            {
                var index = i; // Capture for closure
                var finding = Findings[index];
                <div class="finding-item">
                    <div class="finding-header">
                        <div class="finding-number">@(index + 1)</div>
                        <div class="finding-actions">
                            <button type="button" 
                                    class="remove-finding-btn" 
                                    @onclick="@(() => RemoveFinding(index))"
                                    title="Remove finding">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Finding Description -->
                    <div class="finding-field">
                        <label class="finding-label">
                            <i class="fas fa-edit me-2"></i>Finding Description *
                        </label>
                        <textarea class="finding-input @(HasValidationError(index, "description") ? "error" : "")"
                                  rows="3"
                                  placeholder="Describe what was found wrong or non-conforming..."
                                  value="@finding.Description"
                                  @onchange="@((e) => UpdateFindingDescription(index, e.Value?.ToString() ?? ""))"
                                  @oninput="@((e) => UpdateFindingDescription(index, e.Value?.ToString() ?? ""))">
                        </textarea>
                        @if (HasValidationError(index, "description"))
                        {
                            <div class="field-error">Finding description is required</div>
                        }
                    </div>

                    <!-- Severity Level Selection -->
                    @if (EnableSeveritySelection)
                    {
                        <div class="finding-field">
                            <label class="finding-label">
                                <i class="fas fa-exclamation-circle me-2"></i>Severity Level
                            </label>
                            <div class="severity-selector">
                                @foreach (var severity in Enum.GetValues<SeverityLevel>())
                                {
                                    <button type="button"
                                            class="severity-btn @(finding.SeverityLevel == severity ? "active" : "") @GetSeverityClass(severity)"
                                            @onclick="@(() => UpdateFindingSeverity(index, severity))">
                                        <i class="@GetSeverityIcon(severity) me-2"></i>
                                        @severity
                                    </button>
                                }
                            </div>
                        </div>
                    }

                    <!-- Enhanced Immediate Action -->
                    @if (EnableImmediateActions)
                    {
                        <div class="finding-field">
                            <label class="finding-label">
                                <i class="fas fa-bolt me-2"></i>Immediate Action Taken
                                <span class="optional-label">(Optional)</span>
                            </label>

                            <div class="immediate-action-container">
                                <!-- Action Toggle -->
                                <div class="action-toggle-section">
                                    <label class="action-toggle">
                                        <input type="checkbox"
                                               checked="@finding.HasImmediateAction"
                                               @onchange="@((e) => ToggleImmediateAction(index, (bool)(e.Value ?? false)))" />
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="toggle-label">
                                        @(finding.HasImmediateAction ? "Action Taken" : "No Action")
                                    </span>
                                </div>

                                <!-- Action Details (shown when action is taken) -->
                                @if (finding.HasImmediateAction)
                                {
                                    <div class="action-details-section">
                                        <div class="action-field">
                                            <label class="action-sub-label">Action Description</label>
                                            <textarea class="action-input"
                                                      rows="2"
                                                      placeholder="Describe the immediate corrective action taken on-site..."
                                                      value="@finding.ImmediateAction"
                                                      @onchange="@((e) => UpdateFindingAction(index, e.Value?.ToString() ?? ""))"
                                                      @oninput="@((e) => UpdateFindingAction(index, e.Value?.ToString() ?? ""))">
                                            </textarea>
                                        </div>

                                        @if (finding.ImmediateActionDetails == null)
                                        {
                                            <button type="button"
                                                    class="add-details-btn"
                                                    @onclick="@(() => AddActionDetails(index))">
                                                <i class="fas fa-plus me-1"></i>
                                                Add Details (Who, When, Effectiveness)
                                            </button>
                                        }
                                        else
                                        {
                                            <div class="action-extended-details">
                                                <div class="detail-row">
                                                    <div class="detail-field">
                                                        <label class="action-sub-label">Taken By</label>
                                                        <input type="text"
                                                               class="detail-input"
                                                               placeholder="Name of person who took action"
                                                               value="@finding.ImmediateActionDetails.TakenBy"
                                                               @onchange="@((e) => UpdateActionTakenBy(index, e.Value?.ToString() ?? ""))" />
                                                    </div>
                                                    <div class="detail-field">
                                                        <label class="action-sub-label">Time</label>
                                                        <input type="datetime-local"
                                                               class="detail-input"
                                                               value="@(finding.ImmediateActionDetails.ActionTime?.ToString("yyyy-MM-ddTHH:mm"))"
                                                               @onchange="@((e) => UpdateActionTime(index, e.Value?.ToString()))" />
                                                    </div>
                                                </div>
                                                <div class="detail-field">
                                                    <label class="action-sub-label">Effectiveness Assessment</label>
                                                    <select class="detail-select"
                                                            value="@finding.ImmediateActionDetails.Effectiveness"
                                                            @onchange="@((e) => UpdateActionEffectiveness(index, e.Value?.ToString() ?? "unknown"))">
                                                        <option value="unknown">Not Yet Assessed</option>
                                                        <option value="effective">Effective - Issue Resolved</option>
                                                        <option value="partially_effective">Partially Effective - Needs Follow-up</option>
                                                        <option value="ineffective">Ineffective - Further Action Required</option>
                                                    </select>
                                                </div>
                                                <button type="button"
                                                        class="remove-details-btn"
                                                        @onclick="@(() => RemoveActionDetails(index))">
                                                    <i class="fas fa-times me-1"></i>
                                                    Remove Details
                                                </button>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            }
        }
        else
        {
            <div class="no-findings-message">
                <i class="fas fa-info-circle"></i>
                No findings added yet. Click "Add Finding" to get started.
            </div>
        }
    </div>

    <div class="findings-actions">
        <button type="button" 
                class="add-finding-btn" 
                @onclick="AddFinding">
            <i class="fas fa-plus"></i>
            Add Finding
        </button>
        
        @if (ShowValidationSummary && !IsValid)
        {
            <div class="validation-summary">
                <i class="fas fa-exclamation-triangle"></i>
                At least one complete finding is required for "No" answers
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public List<DetailedFindingModel> Findings { get; set; } = new();
    [Parameter] public EventCallback<List<DetailedFindingModel>> FindingsChanged { get; set; }
    [Parameter] public bool ShowValidationSummary { get; set; } = false;
    [Parameter] public bool EnableSeveritySelection { get; set; } = true;
    [Parameter] public bool EnableImmediateActions { get; set; } = true;

    private HashSet<string> validationErrors = new();

    public bool IsValid => Findings?.Any(f => f.IsValid) == true;

    protected override void OnParametersSet()
    {
        // Ensure we have at least one empty finding to start with
        if (Findings?.Any() != true)
        {
            Findings = new List<DetailedFindingModel> 
            { 
                new DetailedFindingModel 
                { 
                    Description = "", 
                    SeverityLevel = SeverityLevel.Minor,
                    ImmediateAction = ""
                } 
            };
        }
    }

    private async Task AddFinding()
    {
        var newFindings = new List<DetailedFindingModel>(Findings ?? new List<DetailedFindingModel>());
        newFindings.Add(new DetailedFindingModel 
        { 
            Description = "", 
            SeverityLevel = SeverityLevel.Minor,
            ImmediateAction = ""
        });

        await NotifyFindingsChanged(newFindings);
    }

    private async Task RemoveFinding(int index)
    {
        if (index >= 0 && index < Findings.Count)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            newFindings.RemoveAt(index);
            
            // Clear validation errors for this index
            validationErrors.RemoveWhere(e => e.StartsWith($"{index}_"));

            // Ensure we always have at least one finding input
            if (!newFindings.Any())
            {
                newFindings.Add(new DetailedFindingModel 
                { 
                    Description = "", 
                    SeverityLevel = SeverityLevel.Minor,
                    ImmediateAction = ""
                });
            }

            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task UpdateFindingDescription(int index, string value)
    {
        if (index >= 0 && index < Findings.Count)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            newFindings[index].Description = value;

            // Clear validation error if finding is no longer empty
            if (!string.IsNullOrWhiteSpace(value))
            {
                validationErrors.Remove($"{index}_description");
            }
            else
            {
                validationErrors.Add($"{index}_description");
            }

            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task UpdateFindingSeverity(int index, SeverityLevel severity)
    {
        if (index >= 0 && index < Findings.Count)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            newFindings[index].SeverityLevel = severity;
            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task UpdateFindingAction(int index, string value)
    {
        if (index >= 0 && index < Findings.Count)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            newFindings[index].ImmediateAction = value;
            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task ToggleImmediateAction(int index, bool hasAction)
    {
        if (index >= 0 && index < Findings.Count)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            if (!hasAction)
            {
                // Clear action data when toggling off
                newFindings[index].ImmediateAction = "";
                newFindings[index].ImmediateActionDetails = null;
            }
            else if (string.IsNullOrWhiteSpace(newFindings[index].ImmediateAction))
            {
                // Initialize with empty action when toggling on
                newFindings[index].ImmediateAction = "";
            }
            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task AddActionDetails(int index)
    {
        if (index >= 0 && index < Findings.Count)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            newFindings[index].ImmediateActionDetails = new ImmediateActionModel
            {
                Description = newFindings[index].ImmediateAction,
                TakenBy = "",
                ActionTime = DateTime.Now,
                Effectiveness = "unknown"
            };
            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task RemoveActionDetails(int index)
    {
        if (index >= 0 && index < Findings.Count)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            newFindings[index].ImmediateActionDetails = null;
            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task UpdateActionTakenBy(int index, string value)
    {
        if (index >= 0 && index < Findings.Count && Findings[index].ImmediateActionDetails != null)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            newFindings[index].ImmediateActionDetails!.TakenBy = value;
            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task UpdateActionTime(int index, string? value)
    {
        if (index >= 0 && index < Findings.Count && Findings[index].ImmediateActionDetails != null)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            if (DateTime.TryParse(value, out var actionTime))
            {
                newFindings[index].ImmediateActionDetails!.ActionTime = actionTime;
            }
            else
            {
                newFindings[index].ImmediateActionDetails!.ActionTime = null;
            }
            await NotifyFindingsChanged(newFindings);
        }
    }

    private async Task UpdateActionEffectiveness(int index, string value)
    {
        if (index >= 0 && index < Findings.Count && Findings[index].ImmediateActionDetails != null)
        {
            var newFindings = new List<DetailedFindingModel>(Findings);
            newFindings[index].ImmediateActionDetails!.Effectiveness = value;
            await NotifyFindingsChanged(newFindings);
        }
    }

    private bool HasValidationError(int index, string field)
    {
        return ShowValidationSummary && validationErrors.Contains($"{index}_{field}");
    }

    private async Task NotifyFindingsChanged(List<DetailedFindingModel>? newFindings = null)
    {
        var findingsToNotify = newFindings ?? Findings;
        if (FindingsChanged.HasDelegate)
        {
            await FindingsChanged.InvokeAsync(findingsToNotify);
        }
    }

    public void ValidateFindings()
    {
        validationErrors.Clear();
        
        for (int i = 0; i < Findings.Count; i++)
        {
            if (string.IsNullOrWhiteSpace(Findings[i].Description))
            {
                validationErrors.Add($"{i}_description");
            }
        }
    }

    private string GetSeverityClass(SeverityLevel severity)
    {
        return severity.ToString().ToLower() switch
        {
            "critical" => "severity-critical",
            "major" => "severity-major",
            "minor" => "severity-minor",
            "observation" => "severity-observation",
            _ => ""
        };
    }

    private string GetSeverityIcon(SeverityLevel severity)
    {
        return severity.ToString().ToLower() switch
        {
            "critical" => "fas fa-exclamation-triangle",
            "major" => "fas fa-exclamation-circle",
            "minor" => "fas fa-info-circle",
            "observation" => "fas fa-eye",
            _ => "fas fa-circle"
        };
    }
}

<style>
    .enhanced-finding-details-container {
        margin: 1.5rem 0;
        padding: 1.5rem;
        background: linear-gradient(135deg, var(--industrial-gray) 0%, #252525 100%);
        border: 2px solid var(--industrial-border);
        border-radius: 12px;
        border-left: 4px solid var(--industrial-red);
    }

    .findings-header {
        margin-bottom: 1.5rem;
    }

    .findings-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--industrial-red);
        font-size: 1.1rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .findings-title i {
        font-size: 1.2rem;
    }

    .findings-subtitle {
        color: var(--industrial-text-muted);
        font-size: 0.9rem;
        margin: 0;
        line-height: 1.4;
    }

    .findings-list {
        margin-bottom: 1.5rem;
    }

    .finding-item {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--industrial-border-light);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        position: relative;
    }

    .finding-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.25rem;
    }

    .finding-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background: var(--industrial-teal);
        color: white;
        border-radius: 50%;
        font-weight: 700;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .finding-actions {
        display: flex;
        gap: 0.5rem;
    }

    .remove-finding-btn {
        background: var(--industrial-red);
        color: white;
        border: none;
        border-radius: 6px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .remove-finding-btn:hover {
        background: #dc2626;
        transform: scale(1.05);
    }

    .finding-field {
        margin-bottom: 1.25rem;
    }

    .finding-label {
        display: flex;
        align-items: center;
        color: var(--industrial-text);
        font-weight: 600;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .optional-label {
        color: var(--industrial-text-muted);
        font-weight: 400;
        font-size: 0.8rem;
        margin-left: 0.5rem;
        text-transform: none;
        letter-spacing: normal;
    }

    .finding-input, .action-input {
        width: 100%;
        background-color: var(--industrial-light-gray);
        border: 2px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 8px;
        padding: 0.875rem;
        font-size: 0.95rem;
        font-family: inherit;
        transition: all 0.3s ease;
        resize: vertical;
        min-height: 80px;
    }

    .action-input {
        min-height: 60px;
    }

    .finding-input:focus, .action-input:focus {
        background-color: white;
        border-color: var(--industrial-teal);
        color: var(--industrial-dark);
        box-shadow: 0 0 0 0.2rem rgba(20, 184, 166, 0.25);
        outline: none;
    }

    .finding-input.error {
        border-color: var(--industrial-red);
        background-color: rgba(239, 68, 68, 0.1);
    }

    .finding-input::placeholder, .action-input::placeholder {
        color: var(--industrial-text-muted);
    }

    .field-error {
        color: var(--industrial-red);
        font-size: 0.85rem;
        margin-top: 0.5rem;
        font-weight: 500;
    }

    .severity-selector {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
    }

    .severity-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem;
        border: 2px solid var(--industrial-border);
        border-radius: 8px;
        background: var(--industrial-light-gray);
        color: var(--industrial-text);
        font-size: 0.85rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .severity-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .severity-btn.active {
        border-width: 3px;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }

    .severity-btn.severity-critical {
        border-color: var(--industrial-red);
        color: var(--industrial-red);
    }

    .severity-btn.severity-critical.active {
        background: var(--industrial-red);
        color: white;
    }

    .severity-btn.severity-major {
        border-color: #f59e0b;
        color: #f59e0b;
    }

    .severity-btn.severity-major.active {
        background: #f59e0b;
        color: white;
    }

    .severity-btn.severity-minor {
        border-color: var(--industrial-teal);
        color: var(--industrial-teal);
    }

    .severity-btn.severity-minor.active {
        background: var(--industrial-teal);
        color: white;
    }

    /* Enhanced Immediate Action Styles */
    .immediate-action-container {
        background: rgba(0, 0, 0, 0.2);
        border: 1px solid var(--industrial-border);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .action-toggle-section {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .action-toggle {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }

    .action-toggle input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #444;
        transition: 0.3s;
        border-radius: 24px;
        border: 1px solid var(--industrial-border);
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: 0.3s;
        border-radius: 50%;
    }

    .action-toggle input:checked + .toggle-slider {
        background-color: var(--industrial-teal);
    }

    .action-toggle input:checked + .toggle-slider:before {
        transform: translateX(26px);
    }

    .toggle-label {
        font-weight: 500;
        color: var(--industrial-text);
    }

    .action-details-section {
        border-top: 1px solid var(--industrial-border);
        padding-top: 1rem;
    }

    .action-field {
        margin-bottom: 1rem;
    }

    .action-sub-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--industrial-text);
        margin-bottom: 0.5rem;
    }

    .action-input {
        width: 100%;
        padding: 0.75rem;
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid var(--industrial-border);
        border-radius: 6px;
        color: var(--industrial-text);
        font-size: 0.875rem;
        resize: vertical;
        min-height: 60px;
    }

    .action-input:focus {
        outline: none;
        border-color: var(--industrial-teal);
        box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.2);
    }

    .add-details-btn {
        background: transparent;
        border: 1px dashed var(--industrial-border);
        color: var(--industrial-text);
        padding: 0.5rem 1rem;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .add-details-btn:hover {
        border-color: var(--industrial-teal);
        color: var(--industrial-teal);
        background: rgba(20, 184, 166, 0.1);
    }

    .action-extended-details {
        background: rgba(0, 0, 0, 0.2);
        border: 1px solid var(--industrial-border);
        border-radius: 6px;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .detail-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .detail-field {
        margin-bottom: 1rem;
    }

    .detail-input, .detail-select {
        width: 100%;
        padding: 0.5rem;
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid var(--industrial-border);
        border-radius: 4px;
        color: var(--industrial-text);
        font-size: 0.875rem;
    }

    .detail-input:focus, .detail-select:focus {
        outline: none;
        border-color: var(--industrial-teal);
        box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.2);
    }

    .remove-details-btn {
        background: transparent;
        border: 1px solid var(--industrial-red);
        color: var(--industrial-red);
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .remove-details-btn:hover {
        background: var(--industrial-red);
        color: white;
    }

    .severity-btn.severity-observation {
        border-color: #6b7280;
        color: #6b7280;
    }

    .severity-btn.severity-observation.active {
        background: #6b7280;
        color: white;
    }

    .action-indicator {
        display: flex;
        align-items: center;
        margin-top: 0.5rem;
        font-size: 0.85rem;
        color: var(--industrial-text-muted);
    }

    .no-findings-message {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--industrial-text-muted);
        font-style: italic;
        padding: 1.5rem;
        text-align: center;
        justify-content: center;
        background: rgba(20, 184, 166, 0.05);
        border: 1px dashed var(--industrial-border-light);
        border-radius: 8px;
    }

    .findings-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .add-finding-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        background: var(--industrial-teal);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .add-finding-btn:hover {
        background: var(--industrial-teal-dark);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
    }

    .validation-summary {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid var(--industrial-red);
        border-radius: 8px;
        padding: 1rem;
        color: var(--industrial-red);
        font-weight: 500;
        font-size: 0.9rem;
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .enhanced-finding-details-container {
            padding: 1.25rem;
        }

        .finding-item {
            padding: 1.25rem;
        }

        .finding-number {
            width: 32px;
            height: 32px;
            font-size: 0.9rem;
        }

        .remove-finding-btn {
            width: 28px;
            height: 28px;
        }

        .severity-selector {
            grid-template-columns: repeat(2, 1fr);
        }

        .severity-btn {
            padding: 0.625rem;
            font-size: 0.8rem;
        }

        .findings-title {
            font-size: 1rem;
        }

        .add-finding-btn {
            padding: 1rem;
            font-size: 0.9rem;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .finding-input, .action-input {
            padding: 1rem;
            font-size: 16px; /* Prevents zoom on iOS */
            min-height: 88px;
        }

        .action-input {
            min-height: 68px;
        }

        .add-finding-btn {
            padding: 1.25rem;
            min-height: 56px;
        }

        .remove-finding-btn {
            width: 40px;
            height: 40px;
        }

        .finding-number {
            width: 40px;
            height: 40px;
        }

        .severity-btn {
            padding: 1rem;
            min-height: 56px;
        }
    }
</style>
