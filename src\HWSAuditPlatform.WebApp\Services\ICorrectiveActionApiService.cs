using HWSAuditPlatform.WebApp.Models;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for Corrective Action API operations
/// </summary>
public interface ICorrectiveActionApiService
{
    /// <summary>
    /// Gets corrective actions with filtering and pagination
    /// </summary>
    Task<PagedResult<CorrectiveActionDto>?> GetCorrectiveActionsAsync(
        string? searchTerm = null,
        CorrectiveActionStatus? status = null,
        string? findingId = null,
        string? assignedUserId = null,
        bool? isOverdue = null,
        DateTime? dueBefore = null,
        DateTime? dueAfter = null,
        int pageNumber = 1,
        int pageSize = 20,
        string sortBy = "CreatedAt",
        string sortDirection = "desc");

    /// <summary>
    /// Gets a specific corrective action by ID
    /// </summary>
    Task<CorrectiveActionDto?> GetCorrectiveActionAsync(string id);

    /// <summary>
    /// Creates a new corrective action
    /// </summary>
    Task<CorrectiveActionDto?> CreateCorrectiveActionAsync(CreateCorrectiveActionRequest request);

    /// <summary>
    /// Updates corrective action status
    /// </summary>
    Task<bool> UpdateCorrectiveActionStatusAsync(string id, CorrectiveActionStatus status, string? statusNotes = null);

    /// <summary>
    /// Gets corrective actions for a specific finding
    /// </summary>
    Task<List<CorrectiveActionDto>?> GetFindingCorrectiveActionsAsync(string findingId);

    /// <summary>
    /// Gets corrective actions assigned to a specific user
    /// </summary>
    Task<PagedResult<CorrectiveActionDto>?> GetUserCorrectiveActionsAsync(
        string userId,
        CorrectiveActionStatus? status = null,
        int pageNumber = 1,
        int pageSize = 20);

    /// <summary>
    /// Gets overdue corrective actions
    /// </summary>
    Task<PagedResult<CorrectiveActionDto>?> GetOverdueCorrectiveActionsAsync(
        string? assignedUserId = null,
        int pageNumber = 1,
        int pageSize = 20);

    /// <summary>
    /// Gets corrective action statistics
    /// </summary>
    Task<CorrectiveActionStatisticsModel?> GetCorrectiveActionStatisticsAsync(
        string? assignedUserId = null,
        int? factoryId = null,
        int? areaId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null);

    /// <summary>
    /// Assigns a corrective action to a user
    /// </summary>
    Task<bool> AssignCorrectiveActionAsync(string id, string assignedUserId, DateTime dueDate, string? notes = null);

    /// <summary>
    /// Marks a corrective action as completed
    /// </summary>
    Task<bool> CompleteCorrectiveActionAsync(string id, string completionNotes, List<string>? evidenceFileIds = null);

    /// <summary>
    /// Verifies a completed corrective action
    /// </summary>
    Task<bool> VerifyCorrectiveActionAsync(string id, bool isEffective, string verificationNotes);
}

/// <summary>
/// Request model for creating a corrective action
/// </summary>
public class CreateCorrectiveActionRequest
{
    public string FindingId { get; set; } = string.Empty;
    public string ActionDescription { get; set; } = string.Empty;
    public string AssignedUserId { get; set; } = string.Empty;
    public DateTime DueDate { get; set; }
    public string? ActionNotes { get; set; }
}

/// <summary>
/// Request model for updating corrective action status
/// </summary>
public class UpdateCorrectiveActionStatusRequest
{
    public CorrectiveActionStatus Status { get; set; }
    public string? StatusNotes { get; set; }
}

/// <summary>
/// Request model for completing a corrective action
/// </summary>
public class CompleteCorrectiveActionRequest
{
    public string CompletionNotes { get; set; } = string.Empty;
    public List<string>? EvidenceFileIds { get; set; }
}

/// <summary>
/// Request model for verifying a corrective action
/// </summary>
public class VerifyCorrectiveActionRequest
{
    public bool IsEffective { get; set; }
    public string VerificationNotes { get; set; } = string.Empty;
}

/// <summary>
/// Model for corrective action display in UI
/// </summary>
public class CorrectiveActionDto
{
    public string Id { get; set; } = string.Empty;
    public string ActionDescription { get; set; } = string.Empty;
    public CorrectiveActionStatus Status { get; set; }
    public DateTime DueDate { get; set; }
    public bool IsOverdue { get; set; }
    public bool IsCompleted { get; set; }
    public bool IsInProgress { get; set; }
    
    // Finding information
    public string FindingId { get; set; } = string.Empty;
    public string FindingCode { get; set; } = string.Empty;
    public string FindingDescription { get; set; } = string.Empty;
    public SeverityLevel FindingSeverityLevel { get; set; }
    
    // User information
    public string? AssignedUserId { get; set; }
    public string? AssignedUserFullName { get; set; }
    public string? AssignedUserName { get; set; }
    public string? AssignedByUserId { get; set; }
    public string? AssignedByUserFullName { get; set; }
    public string? VerifiedByUserId { get; set; }
    public string? VerifiedByUserFullName { get; set; }
    
    // Audit information
    public string AuditId { get; set; } = string.Empty;
    public string AuditTitle { get; set; } = string.Empty;
    public string AreaName { get; set; } = string.Empty;
    public string FactoryName { get; set; } = string.Empty;
    
    // Notes and completion
    public string? ActionNotes { get; set; }
    public string? CompletionNotes { get; set; }
    public string? VerificationNotes { get; set; }
    public bool? IsEffective { get; set; }
    
    // Dates
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? VerifiedAt { get; set; }
}

/// <summary>
/// Model for corrective action statistics
/// </summary>
public class CorrectiveActionStatisticsModel
{
    public int TotalActions { get; set; }
    public int AssignedActions { get; set; }
    public int InProgressActions { get; set; }
    public int CompletedActions { get; set; }
    public int VerifiedActions { get; set; }
    public int OverdueActions { get; set; }
    public int CancelledActions { get; set; }
    public int IneffectiveActions { get; set; }
    
    // Performance metrics
    public double AverageCompletionDays { get; set; }
    public double OnTimeCompletionRate { get; set; }
    public double EffectivenessRate { get; set; }
    
    // Trends
    public int ActionsCreatedThisMonth { get; set; }
    public int ActionsCompletedThisMonth { get; set; }
    public int ActionsVerifiedThisMonth { get; set; }
}

/// <summary>
/// Model for corrective action filters
/// </summary>
public class CorrectiveActionFilterModel
{
    public string? SearchTerm { get; set; }
    public CorrectiveActionStatus? Status { get; set; }
    public string? FindingId { get; set; }
    public string? AssignedUserId { get; set; }
    public bool? IsOverdue { get; set; }
    public DateTime? DueBefore { get; set; }
    public DateTime? DueAfter { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string SortBy { get; set; } = "CreatedAt";
    public string SortDirection { get; set; } = "desc";
}
