using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.AuditLogs.DTOs;

namespace HWSAuditPlatform.Application.AuditLogs.Queries.GetAuditLogs;

/// <summary>
/// Handler for GetAuditLogsQuery
/// </summary>
public class GetAuditLogsQueryHandler : BaseQueryHandler<GetAuditLogsQuery, PaginatedResult<AuditLogDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAuditLogsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<PaginatedResult<AuditLogDto>> Handle(GetAuditLogsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.AuditLogs
            .Include(al => al.User)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.Filter.UserId))
        {
            query = query.Where(al => al.UserId == request.Filter.UserId);
        }

        if (!string.IsNullOrEmpty(request.Filter.Username))
        {
            query = query.Where(al => al.User != null && al.User.Username.Contains(request.Filter.Username));
        }

        if (!string.IsNullOrEmpty(request.Filter.EntityType))
        {
            query = query.Where(al => al.EntityType == request.Filter.EntityType);
        }

        if (!string.IsNullOrEmpty(request.Filter.EntityId))
        {
            query = query.Where(al => al.EntityId == request.Filter.EntityId);
        }

        if (!string.IsNullOrEmpty(request.Filter.ActionType))
        {
            query = query.Where(al => al.ActionType == request.Filter.ActionType);
        }

        if (request.Filter.FromDate.HasValue)
        {
            query = query.Where(al => al.EventTimestamp >= request.Filter.FromDate.Value);
        }

        if (request.Filter.ToDate.HasValue)
        {
            var toDate = request.Filter.ToDate.Value.Date.AddDays(1); // Include the entire day
            query = query.Where(al => al.EventTimestamp < toDate);
        }

        if (!string.IsNullOrEmpty(request.Filter.IPAddress))
        {
            query = query.Where(al => al.IPAddress == request.Filter.IPAddress);
        }

        if (!string.IsNullOrEmpty(request.Filter.SearchTerm))
        {
            var searchTerm = request.Filter.SearchTerm.ToLower();
            query = query.Where(al => 
                (al.Details != null && al.Details.ToLower().Contains(searchTerm)) ||
                al.EntityType.ToLower().Contains(searchTerm) ||
                al.ActionType.ToLower().Contains(searchTerm) ||
                (al.User != null && (
                    al.User.Username.ToLower().Contains(searchTerm) ||
                    al.User.FirstName.ToLower().Contains(searchTerm) ||
                    al.User.LastName.ToLower().Contains(searchTerm)
                ))
            );
        }

        // Apply sorting
        query = request.Filter.SortBy.ToLower() switch
        {
            "username" => request.Filter.SortDirection.ToLower() == "asc" 
                ? query.OrderBy(al => al.User != null ? al.User.Username : "")
                : query.OrderByDescending(al => al.User != null ? al.User.Username : ""),
            "entitytype" => request.Filter.SortDirection.ToLower() == "asc" 
                ? query.OrderBy(al => al.EntityType)
                : query.OrderByDescending(al => al.EntityType),
            "actiontype" => request.Filter.SortDirection.ToLower() == "asc" 
                ? query.OrderBy(al => al.ActionType)
                : query.OrderByDescending(al => al.ActionType),
            _ => request.Filter.SortDirection.ToLower() == "asc" 
                ? query.OrderBy(al => al.EventTimestamp)
                : query.OrderByDescending(al => al.EventTimestamp)
        };

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var auditLogs = await query
            .Skip((request.Filter.PageNumber - 1) * request.Filter.PageSize)
            .Take(request.Filter.PageSize)
            .Select(al => new AuditLogDto
            {
                Id = (int)al.Id,
                EventTimestamp = al.EventTimestamp,
                ServerReceivedAt = al.ServerReceivedAt,
                UserId = al.UserId,
                Username = al.User != null ? al.User.Username : "Unknown",
                UserFullName = al.User != null ? $"{al.User.FirstName} {al.User.LastName}".Trim() : "Unknown",
                EntityType = al.EntityType,
                EntityId = al.EntityId,
                ActionType = al.ActionType,
                OldValues = al.OldValues,
                NewValues = al.NewValues,
                Details = al.Details,
                IPAddress = al.IPAddress,
                AppVersion = al.AppVersion
            })
            .ToListAsync(cancellationToken);

        return PaginatedResult<AuditLogDto>.Create(auditLogs, totalCount, request.Filter.PageNumber, request.Filter.PageSize);
    }
}
