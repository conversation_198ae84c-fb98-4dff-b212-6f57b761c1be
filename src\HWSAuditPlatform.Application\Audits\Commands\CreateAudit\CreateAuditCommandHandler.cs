using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Services;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.CreateAudit;

/// <summary>
/// Handler for CreateAuditCommand
/// </summary>
public class CreateAuditCommandHandler : BaseAuditableCommandHandler<CreateAuditCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly ITemplateAccessService _templateAccessService;

    public CreateAuditCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ITemplateAccessService templateAccessService,
        IAuditLogService auditLogService) : base(auditLogService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _templateAccessService = templateAccessService;
    }

    public override async Task<string> Handle(CreateAuditCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Check template access
        var hasTemplateAccess = await _templateAccessService.HasTemplateAccessAsync(currentUserId, request.AuditTemplateId, cancellationToken);
        if (!hasTemplateAccess)
        {
            throw new UnauthorizedAccessException($"User does not have access to audit template {request.AuditTemplateId}");
        }

        // Check organizational access
        var hasOrgAccess = await _templateAccessService.HasOrganizationalAccessAsync(
            currentUserId, request.FactoryId, request.AreaId, request.SubAreaId, cancellationToken);
        if (!hasOrgAccess)
        {
            throw new UnauthorizedAccessException($"User does not have access to the specified organizational scope");
        }

        // Get the audit template to ensure it exists and is published
        var auditTemplate = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId && t.IsPublished && t.IsActive,
                cancellationToken);

        if (auditTemplate == null)
        {
            throw new InvalidOperationException($"Audit template with ID {request.AuditTemplateId} not found or not published");
        }

        // Create the audit entity
        var audit = new Audit
        {
            Id = CuidGenerator.Generate(),
            AuditTemplateId = request.AuditTemplateId,
            AssignmentType = request.AssignmentType,
            AssignedToUserGroupId = request.AssignedToUserGroupId,
            AssignedToUserId = request.AssignedToUserId,
            ScheduledDate = request.ScheduledDate,
            DueDate = request.DueDate,
            OverallStatus = AuditOverallStatus.Scheduled,
            FactoryId = request.FactoryId,
            AreaId = request.AreaId,
            SubAreaId = request.SubAreaId,
            CreatedByUserId = _currentUserService.UserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Add to context and save
        await _context.Audits.AddAsync(audit, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        // Log the business operation
        var assignmentDetails = request.AssignmentType == AssignmentType.Individual
            ? $"assigned to user {request.AssignedToUserId}"
            : $"assigned to group {request.AssignedToUserGroupId}";

        await LogBusinessOperationAsync(
            "AuditCreated",
            "Audit",
            audit.Id,
            $"Created audit using template {request.AuditTemplateId}, {assignmentDetails}, scheduled for {request.ScheduledDate:yyyy-MM-dd}",
            cancellationToken);

        return audit.Id;
    }


}
