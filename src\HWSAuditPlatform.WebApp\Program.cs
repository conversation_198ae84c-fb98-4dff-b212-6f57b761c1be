using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Blazored.LocalStorage;
using HWSAuditPlatform.WebApp;
using HWSAuditPlatform.WebApp.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Configure root components
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Configure logging
builder.Logging.SetMinimumLevel(LogLevel.Information);

// Configure HTTP client for API communication
var apiBaseUrl = builder.Configuration["ApiBaseUrl"] ?? "https://localhost:7490/";
builder.Services.AddScoped(sp => new HttpClient
{
    BaseAddress = new Uri(apiBaseUrl)
});

Console.WriteLine($"API Base URL configured: {apiBaseUrl}");

// Configure local storage
builder.Services.AddBlazoredLocalStorage();

// Configure authentication
builder.Services.AddAuthorizationCore();

// Check if debug mode is enabled
var skipAuthentication = builder.Configuration.GetValue<bool>("Debug:SkipAuthentication");

if (skipAuthentication)
{
    Console.WriteLine("🚨 DEBUG MODE ENABLED: Using mock authentication services");

    // Use debug services
    builder.Services.AddScoped<DebugAuthenticationStateProvider>();
    builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<DebugAuthenticationStateProvider>());
    builder.Services.AddScoped<IAuthenticationService, DebugAuthenticationService>();

    Console.WriteLine("🚨 DEBUG MODE: Mock authentication services configured");
}
else
{
    // Use real services
    builder.Services.AddScoped<CustomAuthenticationStateProvider>();
    builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<CustomAuthenticationStateProvider>());
    builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();

    Console.WriteLine("✓ Real authentication services configured");
}

builder.Services.AddScoped<AuthenticatedHttpClientService>();

// Configure validation services
builder.Services.AddScoped<ITemplateValidationService, TemplateValidationService>();

// Configure evidence type service
builder.Services.AddScoped<IEvidenceTypeService, EvidenceTypeService>();

// Configure API services based on debug mode
if (skipAuthentication)
{
    // Use debug API services
    builder.Services.AddScoped<IAuditApiService, DebugAuditApiService>();
    builder.Services.AddScoped<ITemplateApiService, DebugTemplateApiService>();
    builder.Services.AddScoped<IUserApiService, DebugUserApiService>();
    builder.Services.AddScoped<IOrganizationApiService, DebugOrganizationApiService>();
    // Note: IFindingApiService and other services use real implementations even in debug mode
    builder.Services.AddScoped<ICorrectiveActionApiService, DebugCorrectiveActionApiService>();
    builder.Services.AddScoped<IRecurringAuditApiService, DebugRecurringAuditApiService>();

    Console.WriteLine("🚨 DEBUG MODE: Mock API services configured");
}
else
{
    // Use real API services
    builder.Services.AddScoped<IAuditApiService, AuditApiService>();
    builder.Services.AddScoped<ITemplateApiService, TemplateApiService>();
    builder.Services.AddScoped<IUserApiService, UserApiService>();
    builder.Services.AddScoped<IOrganizationApiService, OrganizationApiService>();
    builder.Services.AddScoped<IAreaResponsibilityApiService, AreaResponsibilityApiService>();
    builder.Services.AddScoped<IFindingCategoryApiService, FindingCategoryApiService>();
    builder.Services.AddScoped<IFindingApiService, FindingApiService>();
    builder.Services.AddScoped<ICorrectiveActionApiService, CorrectiveActionApiService>();
    builder.Services.AddScoped<IRecurringAuditApiService, RecurringAuditApiService>();

    Console.WriteLine("✓ Real API services configured");
}

Console.WriteLine("Starting HWS Audit Platform Web App...");

await builder.Build().RunAsync();
