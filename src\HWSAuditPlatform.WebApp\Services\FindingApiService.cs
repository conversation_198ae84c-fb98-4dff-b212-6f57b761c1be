using System.Text;
using System.Text.Json;
using HWSAuditPlatform.WebApp.Models;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Service for Finding API operations
/// </summary>
public class FindingApiService : IFindingApiService
{
    private readonly AuthenticatedHttpClientService _httpClientService;
    private readonly JsonSerializerOptions _jsonOptions;

    public FindingApiService(AuthenticatedHttpClientService httpClientService)
    {
        _httpClientService = httpClientService;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<PagedResult<FindingDto>?> GetFindingsAsync(
        string? searchTerm = null,
        SeverityLevel? severityLevel = null,
        FindingStatus? status = null,
        int? categoryId = null,
        bool? isOverdue = null,
        bool? isResolved = null,
        string? responsibleUserId = null,
        int? factoryId = null,
        int? areaId = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        int pageNumber = 1,
        int pageSize = 20,
        string sortBy = "CreatedAt",
        string sortDirection = "desc")
    {
        var queryParams = new List<string>();

        if (!string.IsNullOrEmpty(searchTerm))
            queryParams.Add($"searchTerm={Uri.EscapeDataString(searchTerm)}");
        if (severityLevel.HasValue)
            queryParams.Add($"severityLevel={severityLevel}");
        if (status.HasValue)
            queryParams.Add($"status={status}");
        if (categoryId.HasValue)
            queryParams.Add($"categoryId={categoryId}");
        if (isOverdue.HasValue)
            queryParams.Add($"isOverdue={isOverdue}");
        if (isResolved.HasValue)
            queryParams.Add($"isResolved={isResolved}");
        if (!string.IsNullOrEmpty(responsibleUserId))
            queryParams.Add($"responsibleUserId={Uri.EscapeDataString(responsibleUserId)}");
        if (factoryId.HasValue)
            queryParams.Add($"factoryId={factoryId}");
        if (areaId.HasValue)
            queryParams.Add($"areaId={areaId}");
        if (createdAfter.HasValue)
            queryParams.Add($"createdAfter={createdAfter:yyyy-MM-dd}");
        if (createdBefore.HasValue)
            queryParams.Add($"createdBefore={createdBefore:yyyy-MM-dd}");

        queryParams.Add($"pageNumber={pageNumber}");
        queryParams.Add($"pageSize={pageSize}");
        queryParams.Add($"sortBy={Uri.EscapeDataString(sortBy)}");
        queryParams.Add($"sortDirection={Uri.EscapeDataString(sortDirection)}");

        var queryString = string.Join("&", queryParams);
        var url = $"api/v1/findings?{queryString}";

        var response = await _httpClientService.GetAsync(url);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<PagedResult<FindingDto>>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<FindingDto?> GetFindingAsync(string id, bool includeCorrectiveActions = false)
    {
        var url = $"api/v1/findings/{Uri.EscapeDataString(id)}";
        if (includeCorrectiveActions)
            url += "?includeCorrectiveActions=true";

        var response = await _httpClientService.GetAsync(url);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<FindingDto>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<FindingDto?> CreateFindingAsync(CreateFindingRequest request)
    {
        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PostAsync("api/v1/findings", content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<FindingDto>>(responseContent, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<FindingDto?> CreateFindingFromAuditAnswerAsync(string auditAnswerId, bool forceCreation = false)
    {
        var url = $"api/v1/findings/from-audit-answer/{Uri.EscapeDataString(auditAnswerId)}";
        if (forceCreation)
            url += "?forceCreation=true";

        var json = JsonSerializer.Serialize(new { }, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PostAsync(url, content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<FindingDto>>(responseContent, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<bool> UpdateFindingStatusAsync(string id, FindingStatus status, string? statusChangeNotes = null)
    {
        var request = new UpdateFindingStatusRequest
        {
            Status = status,
            StatusChangeNotes = statusChangeNotes
        };

        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PutAsync($"api/v1/findings/{Uri.EscapeDataString(id)}/status", content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, _jsonOptions);
            return apiResponse?.Success == true;
        }
        return false;
    }

    public async Task<List<FindingDto>?> GetAuditFindingsAsync(string auditId)
    {
        var response = await _httpClientService.GetAsync($"api/v1/findings/audit/{Uri.EscapeDataString(auditId)}");
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<FindingDto>>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<PagedResult<FindingDto>?> GetOverdueFindingsAsync(
        string? responsibleUserId = null,
        int pageNumber = 1,
        int pageSize = 20)
    {
        var queryParams = new List<string>
        {
            $"pageNumber={pageNumber}",
            $"pageSize={pageSize}"
        };

        if (!string.IsNullOrEmpty(responsibleUserId))
            queryParams.Add($"responsibleUserId={Uri.EscapeDataString(responsibleUserId)}");

        var queryString = string.Join("&", queryParams);
        var url = $"api/v1/findings/overdue?{queryString}";

        var response = await _httpClientService.GetAsync(url);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<PagedResult<FindingDto>>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<FindingStatisticsModel?> GetFindingStatisticsAsync(
        int? factoryId = null,
        int? areaId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        var queryParams = new List<string>();

        if (factoryId.HasValue)
            queryParams.Add($"factoryId={factoryId}");
        if (areaId.HasValue)
            queryParams.Add($"areaId={areaId}");
        if (fromDate.HasValue)
            queryParams.Add($"fromDate={fromDate:yyyy-MM-dd}");
        if (toDate.HasValue)
            queryParams.Add($"toDate={toDate:yyyy-MM-dd}");

        var queryString = queryParams.Any() ? "?" + string.Join("&", queryParams) : "";
        var url = $"api/v1/findings/statistics{queryString}";

        var response = await _httpClientService.GetAsync(url);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<FindingStatisticsModel>>(content, _jsonOptions);
            return apiResponse?.Data;
        }
        return null;
    }

    public async Task<bool> AssignFindingCategoryAsync(string findingId, int? categoryId, string? reason = null)
    {
        var request = new AssignFindingCategoryRequest
        {
            FindingId = findingId,
            CategoryId = categoryId,
            Reason = reason
        };

        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PutAsync($"api/v1/findings/{Uri.EscapeDataString(findingId)}/category", content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, _jsonOptions);
            return apiResponse?.Success == true;
        }
        return false;
    }

    public async Task<bool> AssignFindingResponsibilityAsync(string findingId, string? responsibleUserId, string? retrospectiveAnalystUserId = null)
    {
        var request = new AssignFindingResponsibilityRequest
        {
            ResponsibleUserId = responsibleUserId,
            RetrospectiveAnalystUserId = retrospectiveAnalystUserId
        };

        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        var response = await _httpClientService.PutAsync($"api/v1/findings/{Uri.EscapeDataString(findingId)}/responsibility", content);
        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, _jsonOptions);
            return apiResponse?.Success == true;
        }
        return false;
    }
}
