@page "/findings/reports"
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Enums
@inject IFindingApiService FindingService
@inject ICorrectiveActionApiService CorrectiveActionService
@inject IOrganizationApiService OrganizationService
@inject ILogger<FindingReports> Logger
@inject IJSRuntime JSRuntime

<PageTitle>Finding Reports - HWS Audit Platform</PageTitle>

<div class="finding-reports-container">
    <div class="page-header">
        <h1><i class="bi bi-file-earmark-bar-graph me-3"></i>Finding Reports & Analytics</h1>
        <p class="page-subtitle">Generate comprehensive reports and analyze finding trends</p>
    </div>

    <!-- Report Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-funnel me-2"></i>Report Filters
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Date Range</label>
                    <select class="form-select" @bind="selectedDateRange" @bind:after="OnDateRangeChangedAsync">
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                        <option value="custom">Custom range</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Factory</label>
                    <select class="form-select" @bind="selectedFactoryId">
                        <option value="">All Factories</option>
                        @if (factories != null)
                        {
                            @foreach (var factory in factories)
                            {
                                <option value="@factory.Id">@factory.FactoryName</option>
                            }
                        }
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Severity Level</label>
                    <select class="form-select" @bind="selectedSeverity">
                        <option value="">All Severities</option>
                        <option value="@SeverityLevel.Critical">Critical</option>
                        <option value="@SeverityLevel.Major">Major</option>
                        <option value="@SeverityLevel.Minor">Minor</option>
                        <option value="@SeverityLevel.Observation">Observation</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select class="form-select" @bind="selectedStatus">
                        <option value="">All Statuses</option>
                        <option value="@FindingStatus.Open">Open</option>
                        <option value="@FindingStatus.UnderInvestigation">Under Investigation</option>
                        <option value="@FindingStatus.PendingCorrectiveAction">Pending Corrective Action</option>
                        <option value="@FindingStatus.PendingVerification">Pending Verification</option>
                        <option value="@FindingStatus.Closed">Closed</option>
                    </select>
                </div>
            </div>
            @if (selectedDateRange == "custom")
            {
                <div class="row mt-3">
                    <div class="col-md-3">
                        <label class="form-label">From Date</label>
                        <input type="date" class="form-control" @bind="customFromDate" />
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">To Date</label>
                        <input type="date" class="form-control" @bind="customToDate" />
                    </div>
                </div>
            }
            <div class="row mt-3">
                <div class="col-12">
                    <button class="btn btn-primary me-2" @onclick="GenerateReport" disabled="@isGenerating">
                        @if (isGenerating)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi bi-bar-chart me-2"></i>
                        }
                        Generate Report
                    </button>
                    <button class="btn btn-outline-success me-2" @onclick="ExportToExcel" disabled="@(reportData == null || isExporting)">
                        @if (isExporting)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi bi-file-earmark-excel me-2"></i>
                        }
                        Export to Excel
                    </button>
                    <button class="btn btn-outline-info" @onclick="ExportToPdf" disabled="@(reportData == null || isExporting)">
                        <i class="bi bi-file-earmark-pdf me-2"></i>
                        Export to PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isGenerating)
    {
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Generating report...</span>
            </div>
            <p class="mt-2">Generating report data...</p>
        </div>
    }
    else if (reportData != null)
    {
        <!-- Report Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="report-stat-card">
                    <div class="stat-icon bg-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@reportData.TotalFindings</h3>
                        <p>Total Findings</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="report-stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@reportData.OverdueFindings</h3>
                        <p>Overdue</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="report-stat-card">
                    <div class="stat-icon bg-success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@reportData.ClosedFindings</h3>
                        <p>Resolved</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="report-stat-card">
                    <div class="stat-icon bg-info">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@Math.Round(reportData.AverageResolutionDays, 1)</h3>
                        <p>Avg Resolution Days</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Analytics -->
        <div class="row mb-4">
            <!-- Severity Breakdown -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Severity Breakdown</h5>
                    </div>
                    <div class="card-body">
                        <div class="severity-chart">
                            <div class="severity-item">
                                <span class="severity-label critical">Critical</span>
                                <span class="severity-count">@reportData.CriticalFindings</span>
                                <div class="severity-bar">
                                    <div class="severity-fill critical" style="width: @GetPercentage(reportData.CriticalFindings, reportData.TotalFindings)%"></div>
                                </div>
                            </div>
                            <div class="severity-item">
                                <span class="severity-label major">Major</span>
                                <span class="severity-count">@reportData.MajorFindings</span>
                                <div class="severity-bar">
                                    <div class="severity-fill major" style="width: @GetPercentage(reportData.MajorFindings, reportData.TotalFindings)%"></div>
                                </div>
                            </div>
                            <div class="severity-item">
                                <span class="severity-label minor">Minor</span>
                                <span class="severity-count">@reportData.MinorFindings</span>
                                <div class="severity-bar">
                                    <div class="severity-fill minor" style="width: @GetPercentage(reportData.MinorFindings, reportData.TotalFindings)%"></div>
                                </div>
                            </div>
                            <div class="severity-item">
                                <span class="severity-label observation">Observation</span>
                                <span class="severity-count">@reportData.ObservationFindings</span>
                                <div class="severity-bar">
                                    <div class="severity-fill observation" style="width: @GetPercentage(reportData.ObservationFindings, reportData.TotalFindings)%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trend Analysis -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Trend Analysis</h5>
                    </div>
                    <div class="card-body">
                        <div class="trend-metrics">
                            <div class="trend-item">
                                <div class="trend-label">Findings Created</div>
                                <div class="trend-value">@reportData.FindingsCreatedThisMonth</div>
                                <div class="trend-change positive">
                                    <i class="bi bi-arrow-up"></i>
                                    +12% vs last month
                                </div>
                            </div>
                            <div class="trend-item">
                                <div class="trend-label">Findings Closed</div>
                                <div class="trend-value">@reportData.FindingsClosedThisMonth</div>
                                <div class="trend-change positive">
                                    <i class="bi bi-arrow-up"></i>
                                    +8% vs last month
                                </div>
                            </div>
                            <div class="trend-item">
                                <div class="trend-label">Resolution Rate</div>
                                <div class="trend-value">@GetResolutionRate()%</div>
                                <div class="trend-change @(GetResolutionRate() > 75 ? "positive" : "negative")">
                                    <i class="bi bi-@(GetResolutionRate() > 75 ? "arrow-up" : "arrow-down")"></i>
                                    @(GetResolutionRate() > 75 ? "Good" : "Needs Improvement")
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Issues -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Top Issues by Area</h5>
                    </div>
                    <div class="card-body">
                        @if (topIssuesByArea?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Area</th>
                                            <th>Total Findings</th>
                                            <th>Critical</th>
                                            <th>Major</th>
                                            <th>Open</th>
                                            <th>Overdue</th>
                                            <th>Resolution Rate</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var area in topIssuesByArea.Take(10))
                                        {
                                            <tr>
                                                <td>@area.AreaName</td>
                                                <td><span class="badge bg-primary">@area.TotalFindings</span></td>
                                                <td><span class="badge bg-danger">@area.CriticalFindings</span></td>
                                                <td><span class="badge bg-warning">@area.MajorFindings</span></td>
                                                <td><span class="badge bg-info">@area.OpenFindings</span></td>
                                                <td><span class="badge bg-warning">@area.OverdueFindings</span></td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar @(area.ResolutionRate > 75 ? "bg-success" : area.ResolutionRate > 50 ? "bg-warning" : "bg-danger")" 
                                                             style="width: @area.ResolutionRate%">
                                                            @area.ResolutionRate%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-info-circle fa-2x"></i>
                                <p class="mt-2">No area data available for the selected filters</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Footer -->
        <div class="card">
            <div class="card-body text-center">
                <p class="text-muted mb-0">
                    Report generated on @DateTime.Now.ToString("MMMM dd, yyyy 'at' HH:mm") | 
                    Data range: @GetDateRangeText()
                </p>
            </div>
        </div>
    }
</div>

@code {
    private bool isGenerating = false;
    private bool isExporting = false;
    private string selectedDateRange = "30";
    private string selectedFactoryId = "";
    private string selectedSeverity = "";
    private string selectedStatus = "";
    private DateTime? customFromDate;
    private DateTime? customToDate;

    private FindingStatisticsModel? reportData;
    private List<FactorySummary>? factories;
    private List<AreaFindingStatistics>? topIssuesByArea;

    protected override async Task OnInitializedAsync()
    {
        await LoadFactories();
    }

    private async Task LoadFactories()
    {
        try
        {
            factories = (await OrganizationService.GetFactoriesAsync())?.ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading factories");
        }
    }

    private async Task OnDateRangeChanged(ChangeEventArgs e)
    {
        selectedDateRange = e.Value?.ToString() ?? "30";
        if (selectedDateRange != "custom")
        {
            customFromDate = null;
            customToDate = null;
        }
    }

    private async Task OnDateRangeChangedAsync()
    {
        if (selectedDateRange != "custom")
        {
            customFromDate = null;
            customToDate = null;
        }
    }

    private async Task GenerateReport()
    {
        try
        {
            isGenerating = true;
            StateHasChanged();

            var (fromDate, toDate) = GetDateRange();
            
            // Parse selected values
            SeverityLevel? severity = string.IsNullOrEmpty(selectedSeverity) ? null : Enum.Parse<SeverityLevel>(selectedSeverity);
            FindingStatus? status = string.IsNullOrEmpty(selectedStatus) ? null : Enum.Parse<FindingStatus>(selectedStatus);
            int? factoryId = string.IsNullOrEmpty(selectedFactoryId) ? null : int.Parse(selectedFactoryId);

            // Load report data
            reportData = await FindingService.GetFindingStatisticsAsync(factoryId, null, fromDate, toDate);
            
            // Load top issues by area (mock data for now)
            topIssuesByArea = GenerateMockAreaStatistics();

            Logger.LogInformation("Report generated successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error generating report");
        }
        finally
        {
            isGenerating = false;
            StateHasChanged();
        }
    }

    private async Task ExportToExcel()
    {
        try
        {
            isExporting = true;
            StateHasChanged();

            // Mock export functionality
            await Task.Delay(2000);
            await JSRuntime.InvokeVoidAsync("alert", "Excel export functionality will be implemented with a proper export library");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error exporting to Excel");
        }
        finally
        {
            isExporting = false;
            StateHasChanged();
        }
    }

    private async Task ExportToPdf()
    {
        try
        {
            isExporting = true;
            StateHasChanged();

            // Mock export functionality
            await Task.Delay(2000);
            await JSRuntime.InvokeVoidAsync("alert", "PDF export functionality will be implemented with a proper PDF library");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error exporting to PDF");
        }
        finally
        {
            isExporting = false;
            StateHasChanged();
        }
    }

    private (DateTime?, DateTime?) GetDateRange()
    {
        if (selectedDateRange == "custom")
        {
            return (customFromDate, customToDate);
        }

        var days = int.Parse(selectedDateRange);
        var toDate = DateTime.Now;
        var fromDate = toDate.AddDays(-days);
        return (fromDate, toDate);
    }

    private string GetDateRangeText()
    {
        var (fromDate, toDate) = GetDateRange();
        if (fromDate.HasValue && toDate.HasValue)
        {
            return $"{fromDate.Value:MMM dd, yyyy} - {toDate.Value:MMM dd, yyyy}";
        }
        return "All time";
    }

    private double GetPercentage(int value, int total)
    {
        return total > 0 ? (value * 100.0 / total) : 0;
    }

    private int GetResolutionRate()
    {
        if (reportData == null) return 0;
        var total = reportData.TotalFindings;
        var closed = reportData.ClosedFindings;
        return total > 0 ? (int)Math.Round(closed * 100.0 / total) : 0;
    }

    private List<AreaFindingStatistics> GenerateMockAreaStatistics()
    {
        // Mock data - in real implementation, this would come from the API
        return new List<AreaFindingStatistics>
        {
            new() { AreaName = "Production Line A", TotalFindings = 15, CriticalFindings = 2, MajorFindings = 5, OpenFindings = 3, OverdueFindings = 1, ResolutionRate = 80 },
            new() { AreaName = "Quality Control", TotalFindings = 12, CriticalFindings = 1, MajorFindings = 3, OpenFindings = 2, OverdueFindings = 0, ResolutionRate = 83 },
            new() { AreaName = "Warehouse", TotalFindings = 8, CriticalFindings = 0, MajorFindings = 2, OpenFindings = 1, OverdueFindings = 0, ResolutionRate = 88 },
            new() { AreaName = "Maintenance", TotalFindings = 10, CriticalFindings = 1, MajorFindings = 4, OpenFindings = 4, OverdueFindings = 2, ResolutionRate = 60 },
            new() { AreaName = "Safety Zone", TotalFindings = 6, CriticalFindings = 3, MajorFindings = 2, OpenFindings = 2, OverdueFindings = 1, ResolutionRate = 67 }
        };
    }

    public class AreaFindingStatistics
    {
        public string AreaName { get; set; } = "";
        public int TotalFindings { get; set; }
        public int CriticalFindings { get; set; }
        public int MajorFindings { get; set; }
        public int OpenFindings { get; set; }
        public int OverdueFindings { get; set; }
        public int ResolutionRate { get; set; }
    }
}

<style>
    .finding-reports-container {
        padding: 1rem;
    }

    .page-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .page-header h1 {
        color: var(--bs-dark);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        color: var(--bs-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    /* Report Statistics Cards */
    .report-stat-card {
        display: flex;
        align-items: center;
        padding: 1.5rem;
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        transition: transform 0.2s ease;
    }

    .report-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .report-stat-card .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: white;
        font-size: 1.5rem;
    }

    .report-stat-card .stat-content h3 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        color: var(--bs-dark);
    }

    .report-stat-card .stat-content p {
        margin: 0;
        color: var(--bs-secondary);
        font-size: 0.9rem;
    }

    /* Severity Chart */
    .severity-chart {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .severity-item {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .severity-label {
        min-width: 100px;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 0.375rem;
        color: white;
        text-align: center;
        font-size: 0.875rem;
    }

    .severity-label.critical { background-color: #dc3545; }
    .severity-label.major { background-color: #fd7e14; }
    .severity-label.minor { background-color: #ffc107; color: #000; }
    .severity-label.observation { background-color: #6c757d; }

    .severity-count {
        min-width: 40px;
        font-weight: 600;
        color: var(--bs-dark);
        text-align: center;
    }

    .severity-bar {
        flex: 1;
        height: 20px;
        background-color: var(--bs-light);
        border-radius: 10px;
        overflow: hidden;
    }

    .severity-fill {
        height: 100%;
        transition: width 0.3s ease;
    }

    .severity-fill.critical { background-color: #dc3545; }
    .severity-fill.major { background-color: #fd7e14; }
    .severity-fill.minor { background-color: #ffc107; }
    .severity-fill.observation { background-color: #6c757d; }

    /* Trend Analysis */
    .trend-metrics {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .trend-item {
        padding: 1rem;
        border: 1px solid var(--bs-border-color);
        border-radius: 0.375rem;
        background: var(--bs-light);
    }

    .trend-label {
        font-size: 0.875rem;
        color: var(--bs-secondary);
        margin-bottom: 0.5rem;
    }

    .trend-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--bs-dark);
        margin-bottom: 0.25rem;
    }

    .trend-change {
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .trend-change.positive {
        color: #28a745;
    }

    .trend-change.negative {
        color: #dc3545;
    }

    /* Table Styling */
    .table-hover tbody tr:hover {
        background-color: var(--bs-light);
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: var(--bs-dark);
        background-color: var(--bs-light);
    }

    .progress {
        background-color: var(--bs-light);
    }

    .progress-bar {
        font-size: 0.75rem;
        font-weight: 600;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .report-stat-card {
            flex-direction: column;
            text-align: center;
        }

        .report-stat-card .stat-icon {
            margin-right: 0;
            margin-bottom: 1rem;
        }

        .severity-item {
            flex-direction: column;
            align-items: stretch;
            gap: 0.5rem;
        }

        .severity-label {
            min-width: auto;
        }

        .trend-metrics {
            gap: 1rem;
        }

        .table-responsive {
            font-size: 0.875rem;
        }
    }

    /* Print Styles */
    @@media print {
        .finding-reports-container {
            padding: 0;
        }

        .card {
            border: 1px solid #dee2e6 !important;
            box-shadow: none !important;
        }

        .btn {
            display: none;
        }

        .report-stat-card:hover {
            transform: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }
    }
</style>
