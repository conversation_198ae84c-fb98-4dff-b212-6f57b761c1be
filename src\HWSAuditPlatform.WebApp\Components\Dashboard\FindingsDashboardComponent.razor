@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.Domain.Enums
@inject IFindingApiService FindingService
@inject ICorrectiveActionApiService CorrectiveActionService
@inject ILogger<FindingsDashboardComponent> Logger
@inject NavigationManager Navigation

<div class="findings-dashboard">
    <div class="dashboard-header">
        <h2><i class="bi bi-exclamation-triangle-fill me-3"></i>Findings & Corrective Actions Dashboard</h2>
        <p class="dashboard-subtitle">Monitor findings, track corrective actions, and analyze safety performance</p>
    </div>

    @if (isLoading)
    {
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading dashboard data...</span>
            </div>
            <p class="mt-2">Loading findings dashboard...</p>
        </div>
    }
    else
    {
        <!-- Key Performance Indicators -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card stat-card-danger">
                    <div class="stat-icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@(findingStats?.TotalFindings ?? 0)</h3>
                        <p>Total Findings</p>
                        <small class="text-muted">@(findingStats?.FindingsCreatedThisMonth ?? 0) this month</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card stat-card-warning">
                    <div class="stat-icon">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@(findingStats?.OverdueFindings ?? 0)</h3>
                        <p>Overdue Items</p>
                        <small class="text-muted">@(correctiveActionStats?.OverdueActions ?? 0) actions overdue</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card stat-card-success">
                    <div class="stat-icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@(findingStats?.ClosedFindings ?? 0)</h3>
                        <p>Resolved</p>
                        <small class="text-muted">@(findingStats?.FindingsClosedThisMonth ?? 0) this month</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card stat-card-info">
                    <div class="stat-icon">
                        <i class="bi bi-arrow-repeat"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@(correctiveActionStats?.InProgressActions ?? 0)</h3>
                        <p>In Progress</p>
                        <small class="text-muted">@(Math.Round(findingStats?.AverageResolutionDays ?? 0, 1)) avg days</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics Row -->
        <div class="row mb-4">
            <!-- Severity Distribution -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart me-2"></i>Findings by Severity
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="severity-distribution">
                            <div class="severity-item severity-critical">
                                <div class="severity-label">
                                    <span class="severity-badge critical">Critical</span>
                                    <span class="severity-count">@(findingStats?.CriticalFindings ?? 0)</span>
                                </div>
                                <div class="severity-bar">
                                    <div class="severity-fill critical" 
                                         style="width: @GetSeverityPercentage(findingStats?.CriticalFindings ?? 0)%"></div>
                                </div>
                            </div>
                            <div class="severity-item severity-major">
                                <div class="severity-label">
                                    <span class="severity-badge major">Major</span>
                                    <span class="severity-count">@(findingStats?.MajorFindings ?? 0)</span>
                                </div>
                                <div class="severity-bar">
                                    <div class="severity-fill major" 
                                         style="width: @GetSeverityPercentage(findingStats?.MajorFindings ?? 0)%"></div>
                                </div>
                            </div>
                            <div class="severity-item severity-minor">
                                <div class="severity-label">
                                    <span class="severity-badge minor">Minor</span>
                                    <span class="severity-count">@(findingStats?.MinorFindings ?? 0)</span>
                                </div>
                                <div class="severity-bar">
                                    <div class="severity-fill minor" 
                                         style="width: @GetSeverityPercentage(findingStats?.MinorFindings ?? 0)%"></div>
                                </div>
                            </div>
                            <div class="severity-item severity-observation">
                                <div class="severity-label">
                                    <span class="severity-badge observation">Observation</span>
                                    <span class="severity-count">@(findingStats?.ObservationFindings ?? 0)</span>
                                </div>
                                <div class="severity-bar">
                                    <div class="severity-fill observation" 
                                         style="width: @GetSeverityPercentage(findingStats?.ObservationFindings ?? 0)%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Overview -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pie-chart me-2"></i>Status Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="status-overview">
                            <div class="status-circle-container">
                                <div class="status-circle">
                                    <div class="circle-progress" data-percentage="@GetResolutionPercentage()">
                                        <div class="circle-inner">
                                            <span class="percentage">@GetResolutionPercentage()%</span>
                                            <small>Resolved</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="status-legend">
                                <div class="legend-item">
                                    <span class="legend-color open"></span>
                                    <span class="legend-label">Open (@(findingStats?.OpenFindings ?? 0))</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-color closed"></span>
                                    <span class="legend-label">Closed (@(findingStats?.ClosedFindings ?? 0))</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-color overdue"></span>
                                    <span class="legend-label">Overdue (@(findingStats?.OverdueFindings ?? 0))</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Findings and Actions -->
        <div class="row mb-4">
            <!-- Recent Critical Findings -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>Recent Critical Findings
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" @onclick="NavigateToFindings">
                            View All
                        </button>
                    </div>
                    <div class="card-body">
                        @if (recentCriticalFindings?.Any() == true)
                        {
                            <div class="findings-list">
                                @foreach (var finding in recentCriticalFindings.Take(5))
                                {
                                    <div class="finding-item critical" @onclick="@(() => NavigateToFinding(finding.Id))">
                                        <div class="finding-header">
                                            <span class="finding-code">@finding.FindingCode</span>
                                            <span class="finding-date">@finding.CreatedAt.ToString("MMM dd")</span>
                                        </div>
                                        <div class="finding-description">@finding.FindingDescription</div>
                                        <div class="finding-location">
                                            <i class="bi bi-geo-alt me-1"></i>
                                            @finding.FactoryName - @finding.AreaName
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center text-muted py-3">
                                <i class="bi bi-check-circle fa-2x"></i>
                                <p class="mt-2">No recent critical findings</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Overdue Corrective Actions -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history text-warning me-2"></i>Overdue Actions
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" @onclick="NavigateToCorrectiveActions">
                            View All
                        </button>
                    </div>
                    <div class="card-body">
                        @if (overdueActions?.Any() == true)
                        {
                            <div class="actions-list">
                                @foreach (var action in overdueActions.Take(5))
                                {
                                    <div class="action-item overdue" @onclick="@(() => NavigateToCorrectiveAction(action.Id))">
                                        <div class="action-header">
                                            <span class="action-assignee">@action.AssignedUserName</span>
                                            <span class="action-due">Due @action.DueDate.ToString("MMM dd")</span>
                                        </div>
                                        <div class="action-description">@action.ActionDescription</div>
                                        <div class="action-finding">
                                            <i class="bi bi-link-45deg me-1"></i>
                                            Finding: @action.FindingCode
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center text-muted py-3">
                                <i class="bi bi-check-circle fa-2x"></i>
                                <p class="mt-2">No overdue actions</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning-fill me-2"></i>Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary w-100" @onclick="NavigateToFindings">
                                    <i class="bi bi-search me-2"></i>
                                    Browse All Findings
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning w-100" @onclick="NavigateToOverdueItems">
                                    <i class="bi bi-clock-history me-2"></i>
                                    Review Overdue Items
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success w-100" @onclick="NavigateToCorrectiveActions">
                                    <i class="bi bi-arrow-repeat me-2"></i>
                                    Manage Actions
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info w-100" @onclick="NavigateToReports">
                                    <i class="bi bi-file-earmark-bar-graph me-2"></i>
                                    Generate Reports
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private bool isLoading = true;
    private FindingStatisticsModel? findingStats;
    private CorrectiveActionStatisticsModel? correctiveActionStats;
    private List<FindingDto>? recentCriticalFindings;
    private List<CorrectiveActionDto>? overdueActions;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Load statistics
            var findingStatsTask = FindingService.GetFindingStatisticsAsync();
            var correctiveActionStatsTask = CorrectiveActionService.GetCorrectiveActionStatisticsAsync();

            // Load recent critical findings
            var criticalFindingsTask = FindingService.GetFindingsAsync(
                severityLevel: SeverityLevel.Critical,
                pageSize: 10,
                sortBy: "CreatedAt",
                sortDirection: "desc");

            // Load overdue corrective actions
            var overdueActionsTask = CorrectiveActionService.GetOverdueCorrectiveActionsAsync(pageSize: 10);

            await Task.WhenAll(findingStatsTask, correctiveActionStatsTask, criticalFindingsTask, overdueActionsTask);

            findingStats = await findingStatsTask;
            correctiveActionStats = await correctiveActionStatsTask;
            recentCriticalFindings = (await criticalFindingsTask)?.Items?.ToList();
            overdueActions = (await overdueActionsTask)?.Items?.ToList();

            Logger.LogInformation("Findings dashboard data loaded successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading findings dashboard data");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private double GetSeverityPercentage(int count)
    {
        var total = findingStats?.TotalFindings ?? 0;
        return total > 0 ? (count * 100.0 / total) : 0;
    }

    private int GetResolutionPercentage()
    {
        var total = findingStats?.TotalFindings ?? 0;
        var closed = findingStats?.ClosedFindings ?? 0;
        return total > 0 ? (int)Math.Round(closed * 100.0 / total) : 0;
    }

    // Navigation methods
    private void NavigateToFindings() => Navigation.NavigateTo("/findings");
    private void NavigateToCorrectiveActions() => Navigation.NavigateTo("/corrective-actions");
    private void NavigateToOverdueItems() => Navigation.NavigateTo("/findings?filter=overdue");
    private void NavigateToReports() => Navigation.NavigateTo("/findings/reports");
    private void NavigateToFinding(string id) => Navigation.NavigateTo($"/findings/{id}");
    private void NavigateToCorrectiveAction(string id) => Navigation.NavigateTo($"/corrective-actions/{id}");
}

<style>
    .findings-dashboard {
        padding: 1rem;
    }

    .dashboard-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .dashboard-header h2 {
        color: var(--bs-dark);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .dashboard-subtitle {
        color: var(--bs-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    /* Severity Distribution */
    .severity-distribution {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .severity-item {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .severity-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .severity-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: white;
    }

    .severity-badge.critical { background-color: #dc3545; }
    .severity-badge.major { background-color: #fd7e14; }
    .severity-badge.minor { background-color: #ffc107; color: #000; }
    .severity-badge.observation { background-color: #6c757d; }

    .severity-count {
        font-weight: 600;
        color: var(--bs-dark);
    }

    .severity-bar {
        height: 8px;
        background-color: var(--bs-light);
        border-radius: 4px;
        overflow: hidden;
    }

    .severity-fill {
        height: 100%;
        transition: width 0.3s ease;
    }

    .severity-fill.critical { background-color: #dc3545; }
    .severity-fill.major { background-color: #fd7e14; }
    .severity-fill.minor { background-color: #ffc107; }
    .severity-fill.observation { background-color: #6c757d; }

    /* Status Overview */
    .status-overview {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .status-circle-container {
        flex-shrink: 0;
    }

    .status-circle {
        position: relative;
        width: 120px;
        height: 120px;
    }

    .circle-progress {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: conic-gradient(#28a745 0deg, #28a745 var(--percentage, 0)deg, #e9ecef var(--percentage, 0)deg);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .circle-inner {
        width: 80px;
        height: 80px;
        background: white;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    .percentage {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--bs-dark);
        line-height: 1;
    }

    .circle-inner small {
        font-size: 0.75rem;
        color: var(--bs-secondary);
        margin-top: 0.25rem;
    }

    .status-legend {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
    }

    .legend-color.open { background-color: #ffc107; }
    .legend-color.closed { background-color: #28a745; }
    .legend-color.overdue { background-color: #dc3545; }

    .legend-label {
        font-size: 0.875rem;
        color: var(--bs-dark);
    }

    /* Findings and Actions Lists */
    .findings-list, .actions-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        max-height: 300px;
        overflow-y: auto;
    }

    .finding-item, .action-item {
        padding: 0.75rem;
        border: 1px solid var(--bs-border-color);
        border-radius: 0.375rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .finding-item:hover, .action-item:hover {
        border-color: var(--bs-primary);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .finding-item.critical {
        border-left: 4px solid #dc3545;
    }

    .action-item.overdue {
        border-left: 4px solid #ffc107;
    }

    .finding-header, .action-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .finding-code, .action-assignee {
        font-weight: 600;
        color: var(--bs-primary);
    }

    .finding-date, .action-due {
        font-size: 0.875rem;
        color: var(--bs-secondary);
    }

    .finding-description, .action-description {
        font-size: 0.875rem;
        color: var(--bs-dark);
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .finding-location, .action-finding {
        font-size: 0.75rem;
        color: var(--bs-secondary);
        display: flex;
        align-items: center;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .status-overview {
            flex-direction: column;
            text-align: center;
        }

        .severity-distribution {
            gap: 0.75rem;
        }

        .findings-list, .actions-list {
            max-height: 250px;
        }
    }
</style>
