@page "/audits"
@attribute [Authorize]
@implements IDisposable

<PageTitle>Audits - HWS Audit Platform</PageTitle>

<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-1">
                        <i class="bi bi-clipboard-check me-2 text-primary"></i>
                        Audit Management
                    </h1>
                    <p class="text-muted mb-0">Create, assign, and manage audits</p>
                </div>
                <div>
                    <button class="btn btn-primary" @onclick="CreateNewAudit">
                        <i class="bi bi-plus-circle me-2"></i>Create Audit
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" placeholder="Search audits..." @bind="searchTerm" @bind:event="oninput" @bind:after="OnSearchTermChanged" @onkeypress="@(async (e) => { if (e.Key == "Enter") await SearchAudits(); })" />
                <button class="btn btn-outline-secondary" @onclick="SearchAudits">Search</button>
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" @bind="statusFilter" @bind:after="OnStatusFilterChanged">
                <option value="">All Statuses</option>
                <option value="Scheduled">Scheduled</option>
                <option value="InProgress">In Progress</option>
                <option value="Submitted">Submitted</option>
                <option value="Overdue">Overdue</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" @bind="templateFilter" @bind:after="OnTemplateFilterChanged">
                <option value="">All Templates</option>
                @if (templates != null)
                {
                    @foreach (var template in templates)
                    {
                        <option value="@template.Id">@template.TemplateName</option>
                    }
                }
            </select>
        </div>
        <div class="col-md-2">
            <button class="btn btn-outline-secondary w-100" @onclick="ClearFilters">
                <i class="bi bi-x-circle me-1"></i>Clear
            </button>
        </div>
    </div>

    <!-- Audit List -->
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading audits...</p>
        </div>
    }
    else if (audits?.Any() == true)
    {
        <div class="row">
            @foreach (var audit in audits)
            {
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">@audit.AuditTemplate?.TemplateName</h6>
                            <span class="badge @GetStatusBadgeClass(audit.OverallStatus)">
                                @audit.OverallStatus
                            </span>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                <strong>Location:</strong> @audit.LocationDescription<br />
                                <strong>Scheduled:</strong> @audit.ScheduledDate.ToString("MMM dd, yyyy")<br />
                                @if (audit.AssignedToUser != null)
                                {
                                    <strong>Assigned to:</strong> @audit.AssignedToUser.FullName<br />
                                }
                                @if (audit.DueDate.HasValue)
                                {
                                    <strong>Due:</strong> @audit.DueDate.Value.ToString("MMM dd, yyyy")
                                }
                            </p>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <button class="btn btn-outline-primary btn-sm" @onclick="() => ViewAudit(audit.Id)">
                                    <i class="bi bi-eye me-1"></i>View
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" @onclick="() => EditAudit(audit.Id)">
                                    <i class="bi bi-pencil me-1"></i>Edit
                                </button>
                                @if (audit.OverallStatus == AuditOverallStatus.Scheduled)
                                {
                                    <button class="btn btn-outline-danger btn-sm" @onclick="() => DeleteAudit(audit.Id)">
                                        <i class="bi bi-trash me-1"></i>Delete
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="bi bi-clipboard-x display-1 text-muted"></i>
            <h3 class="mt-3">No audits found</h3>
            <p class="text-muted">Create your first audit to get started.</p>
            <button class="btn btn-primary" @onclick="CreateNewAudit">
                <i class="bi bi-plus-circle me-2"></i>Create Audit
            </button>
        </div>
    }
</div>

@code {
    private IEnumerable<Audit>? audits;
    private IEnumerable<AuditTemplate>? templates;
    private bool isLoading = true;
    private string searchTerm = string.Empty;
    private string statusFilter = string.Empty;
    private string templateFilter = string.Empty;
    private Timer? _searchTimer;

    [Inject] private IAuditApiService AuditService { get; set; } = default!;
    [Inject] private ITemplateApiService TemplateService { get; set; } = default!;
    [Inject] private NavigationManager Navigation { get; set; } = default!;
    [Inject] private ILogger<Audits> Logger { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;

            // Parse template filter
            int? templateIdFilter = int.TryParse(templateFilter, out var tid) ? tid : null;

            var auditTask = AuditService.GetAuditsAsync(
                string.IsNullOrWhiteSpace(searchTerm) ? null : searchTerm,
                string.IsNullOrWhiteSpace(statusFilter) ? null : statusFilter,
                templateIdFilter);
            var templateTask = TemplateService.GetTemplatesAsync();

            await Task.WhenAll(auditTask, templateTask);

            audits = await auditTask;
            templates = await templateTask;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading audit data");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchAudits()
    {
        await LoadData();
    }

    private async Task OnStatusFilterChanged()
    {
        await LoadData();
    }

    private async Task OnTemplateFilterChanged()
    {
        await LoadData();
    }

    private void OnSearchTermChanged()
    {
        // Debounce search to avoid too many API calls
        _searchTimer?.Dispose();
        _searchTimer = new Timer(async _ =>
        {
            await InvokeAsync(async () =>
            {
                await LoadData();
                StateHasChanged();
            });
        }, null, TimeSpan.FromMilliseconds(500), Timeout.InfiniteTimeSpan);
    }

    private async Task ClearFilters()
    {
        searchTerm = string.Empty;
        statusFilter = string.Empty;
        templateFilter = string.Empty;
        await LoadData();
    }

    private string GetStatusBadgeClass(AuditOverallStatus status)
    {
        return status switch
        {
            AuditOverallStatus.Scheduled => "bg-info",
            AuditOverallStatus.InProgress => "bg-warning",
            AuditOverallStatus.Submitted => "bg-success",
            AuditOverallStatus.Closed => "bg-secondary",
            AuditOverallStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private void CreateNewAudit()
    {
        Navigation.NavigateTo("/audits/create");
    }

    private void ViewAudit(string auditId)
    {
        Navigation.NavigateTo($"/audits/{auditId}");
    }

    private void EditAudit(string auditId)
    {
        Navigation.NavigateTo($"/audits/{auditId}/edit");
    }

    private async Task DeleteAudit(string auditId)
    {
        if (await AuditService.DeleteAuditAsync(auditId))
        {
            await LoadData();
        }
    }

    public void Dispose()
    {
        _searchTimer?.Dispose();
    }
}
