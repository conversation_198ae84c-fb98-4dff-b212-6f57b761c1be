using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.AuditLogs.Queries.GetAuditLog;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Workflow;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Application.AuditLogs.Queries;

public class GetAuditLogQueryHandlerTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly GetAuditLogQueryHandler _handler;
    private readonly int _testAuditLogId;

    public GetAuditLogQueryHandlerTests()
    {
        _context = TestDbContextFactory.CreateInMemoryContext();
        _handler = new GetAuditLogQueryHandler(_context);

        _testAuditLogId = SeedTestData();
    }

    private int SeedTestData()
    {
        // Create test user
        var user = User.Create(
            username: "testuser",
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: "test-user-guid-123456789012345678901234567890",
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        _context.Users.Add(user);

        // Create test audit log
        var auditLog = new AuditLog
        {
            EventTimestamp = DateTime.UtcNow,
            ServerReceivedAt = DateTime.UtcNow,
            UserId = user.AdObjectGuid,
            EntityType = "User",
            EntityId = "test-entity-id",
            ActionType = "Create",
            NewValues = "{\"Name\":\"Test Entity\",\"Email\":\"<EMAIL>\"}",
            Details = "Created test entity",
            IPAddress = "*************",
            AppVersion = "1.0.0"
        };

        _context.AuditLogs.Add(auditLog);
        _context.SaveChanges();

        return (int)auditLog.Id;
    }

    [Fact]
    public async Task Handle_WithValidId_ShouldReturnAuditLog()
    {
        // Arrange
        var query = new GetAuditLogQuery { Id = _testAuditLogId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_testAuditLogId, result.Id);
        Assert.Equal("User", result.EntityType);
        Assert.Equal("test-entity-id", result.EntityId);
        Assert.Equal("Create", result.ActionType);
        Assert.Equal("testuser", result.Username);
        Assert.Equal("Test User", result.UserFullName);
        Assert.Contains("Test Entity", result.NewValues);
        Assert.Equal("Created test entity", result.Details);
        Assert.Equal("*************", result.IPAddress);
        Assert.Equal("1.0.0", result.AppVersion);
    }

    [Fact]
    public async Task Handle_WithInvalidId_ShouldReturnNull()
    {
        // Arrange
        var query = new GetAuditLogQuery { Id = 99999 };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task Handle_WithAuditLogWithoutUser_ShouldReturnAuditLogWithUnknownUser()
    {
        // Arrange - Create audit log without associated user
        var auditLogWithoutUser = new AuditLog
        {
            EventTimestamp = DateTime.UtcNow,
            ServerReceivedAt = DateTime.UtcNow,
            UserId = "non-existent-user-id",
            EntityType = "System",
            EntityId = "system-entity",
            ActionType = "SystemEvent",
            Details = "System event without user",
            IPAddress = "127.0.0.1",
            AppVersion = "1.0.0"
        };

        _context.AuditLogs.Add(auditLogWithoutUser);
        await _context.SaveChangesAsync();

        var query = new GetAuditLogQuery { Id = (int)auditLogWithoutUser.Id };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Unknown", result.Username);
        Assert.Equal("Unknown", result.UserFullName);
        Assert.Equal("non-existent-user-id", result.UserId);
    }

    [Fact]
    public async Task Handle_ShouldIncludeAllAuditLogProperties()
    {
        // Arrange
        var query = new GetAuditLogQuery { Id = _testAuditLogId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Id > 0);
        Assert.True(result.EventTimestamp > DateTime.MinValue);
        Assert.True(result.ServerReceivedAt > DateTime.MinValue);
        Assert.NotEmpty(result.UserId);
        Assert.NotEmpty(result.Username);
        Assert.NotEmpty(result.UserFullName);
        Assert.NotEmpty(result.EntityType);
        Assert.NotEmpty(result.EntityId);
        Assert.NotEmpty(result.ActionType);
        Assert.NotNull(result.NewValues);
        Assert.NotEmpty(result.Details);
        Assert.NotNull(result.IPAddress);
        Assert.NotNull(result.AppVersion);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
