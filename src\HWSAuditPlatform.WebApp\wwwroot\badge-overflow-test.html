<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Badge Overflow Test - HWS Audit Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/app.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 300px;
            border: 2px solid #ccc;
            padding: 1rem;
            margin: 1rem 0;
            background: #f8f9fa;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #495057;
        }
    </style>
</head>
<body class="bg-dark text-light">
    <div class="container mt-4">
        <h1 class="mb-4">Badge Overflow Solutions Test</h1>
        
        <!-- Test 1: Long text badges without container -->
        <div class="test-container">
            <div class="test-title">❌ Before: Long badges without container (overflow issue)</div>
            <span class="badge bg-primary" style="max-width: none;">This is a very long badge text that will overflow</span>
            <span class="badge bg-success" style="max-width: none;">Another extremely long badge text example</span>
        </div>

        <!-- Test 2: Long text badges with new solution -->
        <div class="test-container">
            <div class="test-title">✅ After: Long badges with badge-container</div>
            <div class="badge-container">
                <span class="badge bg-primary" title="This is a very long badge text that will overflow">This is a very long badge text that will overflow</span>
                <span class="badge bg-success" title="Another extremely long badge text example">Another extremely long badge text example</span>
            </div>
        </div>

        <!-- Test 3: Small badges in constrained space -->
        <div class="test-container">
            <div class="test-title">✅ Small badges in constrained container</div>
            <div class="badge-container">
                <span class="badge badge-sm bg-warning" title="Critical Severity Level">Critical Severity Level</span>
                <span class="badge badge-sm bg-info" title="Safety Category Name">Safety Category Name</span>
                <span class="badge badge-sm bg-danger" title="Open Status">Open Status</span>
            </div>
        </div>

        <!-- Test 4: Table cell simulation -->
        <div class="test-container">
            <div class="test-title">✅ Table cell badge simulation</div>
            <table class="table table-dark table-sm">
                <tr>
                    <td style="width: 100px;">Status:</td>
                    <td>
                        <div class="badge-container">
                            <span class="badge badge-sm bg-warning" title="Pending Manager Review">Pending Manager Review</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Score:</td>
                    <td>
                        <div class="badge-container">
                            <span class="badge badge-sm bg-success" title="87.5%">87.5%</span>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Test 5: Multiple badge sizes -->
        <div class="test-container">
            <div class="test-title">✅ Different badge sizes</div>
            <div class="badge-container mb-2">
                <span class="badge badge-xs bg-secondary" title="Extra Small Badge">Extra Small Badge</span>
                <span class="badge badge-sm bg-primary" title="Small Badge">Small Badge</span>
                <span class="badge bg-success" title="Regular Badge">Regular Badge</span>
                <span class="badge badge-lg bg-info" title="Large Badge">Large Badge</span>
            </div>
        </div>

        <!-- Test 6: Responsive behavior simulation -->
        <div class="test-container">
            <div class="test-title">✅ Responsive badges (resize window to test)</div>
            <div class="badge-container">
                <span class="badge bg-primary" title="This badge will resize on mobile">This badge will resize on mobile</span>
                <span class="badge bg-warning" title="Another responsive badge">Another responsive badge</span>
            </div>
        </div>

        <!-- Test 7: Finding card metadata simulation -->
        <div class="test-container">
            <div class="test-title">✅ Finding card metadata style</div>
            <div class="finding-metadata">
                <div class="metadata-row">
                    <span class="metadata-label">Severity:</span>
                    <div class="badge-container">
                        <span class="badge badge-sm bg-danger" title="Critical">Critical</span>
                    </div>
                </div>
                <div class="metadata-row">
                    <span class="metadata-label">Category:</span>
                    <div class="badge-container">
                        <span class="badge badge-sm bg-info" title="Safety and Environmental Compliance">Safety and Environmental Compliance</span>
                    </div>
                </div>
                <div class="metadata-row">
                    <span class="metadata-label">Status:</span>
                    <div class="badge-container">
                        <span class="badge badge-sm bg-warning" title="Open">Open</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info mt-4">
            <h5>Test Instructions:</h5>
            <ul>
                <li>Resize your browser window to test responsive behavior</li>
                <li>Hover over badges to see full text in tooltips</li>
                <li>Compare "Before" vs "After" examples to see the improvement</li>
                <li>Check that badges don't overflow their containers</li>
            </ul>
        </div>
    </div>

    <style>
        /* Simulate finding card metadata styles for test */
        .finding-metadata {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        .metadata-row {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 0;
        }
        .metadata-label {
            font-weight: 500;
            color: #6c757d;
            min-width: 80px;
            font-size: 0.875rem;
            flex-shrink: 0;
        }
        .metadata-row .badge-container {
            flex: 1;
            min-width: 0;
        }
    </style>
</body>
</html>
