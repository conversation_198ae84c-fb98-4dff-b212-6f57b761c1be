using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.CorrectiveActions.DTOs;

/// <summary>
/// Summary DTO for corrective actions in list views
/// </summary>
public class CorrectiveActionSummaryDto
{
    /// <summary>
    /// Unique identifier for the corrective action
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// ID of the finding this corrective action addresses
    /// </summary>
    public string FindingId { get; set; } = string.Empty;

    /// <summary>
    /// Code of the finding this corrective action addresses
    /// </summary>
    public string? FindingCode { get; set; }

    /// <summary>
    /// Description of the corrective action to be taken
    /// </summary>
    public string ActionDescription { get; set; } = string.Empty;

    /// <summary>
    /// User ID of the person assigned to complete this action
    /// </summary>
    public string AssignedToUserId { get; set; } = string.Empty;

    /// <summary>
    /// Full name of the assigned user
    /// </summary>
    public string? AssignedToUserName { get; set; }

    /// <summary>
    /// Due date for completing this corrective action
    /// </summary>
    public DateOnly DueDate { get; set; }

    /// <summary>
    /// Date when the action was completed
    /// </summary>
    public DateOnly? CompletionDate { get; set; }

    /// <summary>
    /// Current status of the corrective action
    /// </summary>
    public CorrectiveActionStatus Status { get; set; }

    /// <summary>
    /// Name of the factory where the corrective action is needed
    /// </summary>
    public string FactoryName { get; set; } = string.Empty;

    /// <summary>
    /// Name of the area where the corrective action is needed
    /// </summary>
    public string AreaName { get; set; } = string.Empty;

    /// <summary>
    /// Whether this corrective action is overdue
    /// </summary>
    public bool IsOverdue { get; set; }

    /// <summary>
    /// Whether this corrective action is completed
    /// </summary>
    public bool IsCompleted { get; set; }

    /// <summary>
    /// Whether this corrective action is in progress
    /// </summary>
    public bool IsInProgress { get; set; }

    /// <summary>
    /// When the corrective action was created
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
