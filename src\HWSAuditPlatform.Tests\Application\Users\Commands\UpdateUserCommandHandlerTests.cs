using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Users.Commands.UpdateUser;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using Moq;

namespace HWSAuditPlatform.Tests.Application.Users.Commands;

public class UpdateUserCommandHandlerTests : BaseDbTestClass
{
    private readonly UpdateUserCommandHandler _handler;
    private readonly Mock<IAuditLogService> _mockAuditLogService;

    public UpdateUserCommandHandlerTests()
    {
        _mockAuditLogService = new Mock<IAuditLogService>();
        _handler = new UpdateUserCommandHandler(Context, MockCurrentUserService.Object, _mockAuditLogService.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldUpdateUser()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var existingUser = await Context.Users.FirstAsync(u => u.Username == "auditor");
        var originalVersion = existingUser.RecordVersion;
        
        var command = new UpdateUserCommand
        {
            Id = existingUser.Id,
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = UserRole.ProcessOwner,
            FactoryId = 1,
            IsActive = false,
            RecordVersion = originalVersion
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedUser = await Context.Users.FirstAsync(u => u.Id == existingUser.Id);
        updatedUser.FirstName.Should().Be(command.FirstName);
        updatedUser.LastName.Should().Be(command.LastName);
        updatedUser.Email.Should().Be(command.Email);
        updatedUser.IsActive.Should().Be(command.IsActive);
        updatedUser.RecordVersion.Should().Be(originalVersion + 1);
        updatedUser.UpdatedByUserId.Should().Be(MockCurrentUserService.Object.UserId);
    }

    [Fact]
    public async Task Handle_WithNonExistentUser_ShouldThrowNotFoundException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var command = new UpdateUserCommand
        {
            Id = "non-existent-id",
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = UserRole.ProcessOwner,
            FactoryId = 1,
            IsActive = true,
            RecordVersion = 1
        };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithInvalidRole_ShouldThrowException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var existingUser = await Context.Users.FirstAsync(u => u.Username == "auditor");
        
        var command = new UpdateUserCommand
        {
            Id = existingUser.Id,
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = (UserRole)999, // Invalid role
            FactoryId = 1,
            IsActive = true,
            RecordVersion = existingUser.RecordVersion
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithDuplicateEmail_ShouldThrowException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var userToUpdate = await Context.Users.FirstAsync(u => u.Username == "auditor");
        var existingEmail = await Context.Users
            .Where(u => u.Username == "admin")
            .Select(u => u.Email)
            .FirstAsync();
        
        var command = new UpdateUserCommand
        {
            Id = userToUpdate.Id,
            FirstName = "Updated",
            LastName = "Name",
            Email = existingEmail, // Duplicate email
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true,
            RecordVersion = userToUpdate.RecordVersion
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithStaleRecordVersion_ShouldThrowConcurrencyException()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var existingUser = await Context.Users.FirstAsync(u => u.Username == "auditor");
        
        var command = new UpdateUserCommand
        {
            Id = existingUser.Id,
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true,
            RecordVersion = existingUser.RecordVersion - 1 // Stale version
        };

        // Act & Assert
        await Assert.ThrowsAsync<ConflictException>(() =>
            _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldUpdateTimestamp()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var existingUser = await Context.Users.FirstAsync(u => u.Username == "auditor");
        var originalUpdatedAt = existingUser.UpdatedAt;
        
        // Wait a bit to ensure timestamp difference
        await Task.Delay(10);
        
        var command = new UpdateUserCommand
        {
            Id = existingUser.Id,
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true,
            RecordVersion = existingUser.RecordVersion
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedUser = await Context.Users.FirstAsync(u => u.Id == existingUser.Id);
        updatedUser.UpdatedAt.Should().BeAfter(originalUpdatedAt);
    }

    [Fact]
    public async Task Handle_WithNullFactoryId_ShouldUpdateUser()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var existingUser = await Context.Users.FirstAsync(u => u.Username == "auditor");
        
        var command = new UpdateUserCommand
        {
            Id = existingUser.Id,
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = UserRole.DevAdmin, // Admin might not have factory
            FactoryId = null,
            IsActive = true,
            RecordVersion = existingUser.RecordVersion
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedUser = await Context.Users.FirstAsync(u => u.Id == existingUser.Id);
        updatedUser.FactoryId.Should().BeNull();
    }

    [Fact]
    public async Task Handle_ShouldNotChangeUsername()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var existingUser = await Context.Users.FirstAsync(u => u.Username == "auditor");
        var originalUsername = existingUser.Username;
        
        var command = new UpdateUserCommand
        {
            Id = existingUser.Id,
            FirstName = "Updated",
            LastName = "Name",
            Email = "<EMAIL>",
            Role = UserRole.Auditor,
            FactoryId = 1,
            IsActive = true,
            RecordVersion = existingUser.RecordVersion
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        var updatedUser = await Context.Users.FirstAsync(u => u.Id == existingUser.Id);
        updatedUser.Username.Should().Be(originalUsername);
    }
}
