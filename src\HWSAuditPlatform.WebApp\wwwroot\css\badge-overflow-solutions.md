# Badge Overflow Solutions

This document explains the badge overflow solutions implemented in the HWS Audit Platform WebApp.

## Problem
Badges were overflowing their containers, especially when containing long text content, causing layout issues and poor user experience.

## Solutions Implemented

### 1. Base Badge Styling
All badges now have:
- `max-width: 100%` - Prevents badges from exceeding container width
- `overflow: hidden` - Hides overflowing content
- `text-overflow: ellipsis` - Shows "..." for truncated text
- `white-space: nowrap` - Prevents text wrapping
- Responsive font sizing that scales down on smaller screens

### 2. Badge Size Variants
- `.badge` - Default size (0.75rem font, max-width varies by screen size)
- `.badge-sm` - Small badges (0.65rem font, 120px max-width)
- `.badge-lg` - Large badges (0.875rem font, 200px max-width)
- `.badge-xs` - Extra small for mobile (0.55rem font, 60px max-width)

### 3. Badge Container System
- `.badge-container` - Flex container for badges with proper overflow handling
- Allows multiple badges to wrap gracefully
- Ensures badges can shrink when needed

### 4. Context-Specific Constraints
- Table cell badges: max-width 120px
- Timeline/card badges: max-width 150px
- Mobile badges: progressively smaller max-widths

### 5. Special Badge Classes
- `.badge-truncate` - For very long text (200px max-width)
- `.badge-wrap` - Allows text wrapping for multi-line badges
- Tooltip support with `title` attribute

## Usage Examples

### Basic Badge with Container
```html
<div class="badge-container">
    <span class="badge badge-sm bg-primary" title="Full Text Here">
        Long Badge Text
    </span>
</div>
```

### Multiple Badges
```html
<div class="badge-container">
    <span class="badge badge-sm bg-success">Status</span>
    <span class="badge badge-sm bg-info">Category</span>
</div>
```

### Table Cell Badge
```html
<td>
    <div class="badge-container">
        <span class="badge badge-sm bg-warning" title="@fullStatusText">
            @statusText
        </span>
    </div>
</td>
```

## Responsive Behavior
- Desktop: Full-size badges with generous max-widths
- Tablet (≤768px): Smaller fonts and reduced max-widths
- Mobile (≤576px): Minimal badges with tight constraints

## Best Practices
1. Always use `badge-container` wrapper for proper flex behavior
2. Add `title` attribute for truncated text accessibility
3. Use appropriate size variant (`badge-sm`, `badge-lg`) based on context
4. Test badges with long text content to ensure proper truncation
