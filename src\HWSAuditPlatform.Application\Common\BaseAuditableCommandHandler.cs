using MediatR;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Application.Common;

/// <summary>
/// Base class for command handlers that include audit logging capabilities
/// </summary>
/// <typeparam name="TCommand">The type of command being handled</typeparam>
public abstract class BaseAuditableCommandHandler<TCommand> : IRequestHandler<TCommand>
    where TCommand : BaseCommand
{
    protected readonly IAuditLogService AuditLogService;

    protected BaseAuditableCommandHandler(IAuditLogService auditLogService)
    {
        AuditLogService = auditLogService;
    }

    public abstract Task Handle(TCommand request, CancellationToken cancellationToken);

    /// <summary>
    /// Logs a business operation for audit purposes
    /// </summary>
    /// <param name="actionType">Type of business action performed</param>
    /// <param name="entityType">Type of entity involved</param>
    /// <param name="entityId">ID of the entity involved</param>
    /// <param name="details">Additional context or details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task LogBusinessOperationAsync(
        string actionType,
        string entityType,
        string entityId,
        string? details = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await AuditLogService.LogBusinessOperationAsync(
                actionType, entityType, entityId, details, cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the main operation
            // This could be enhanced with proper logging
            System.Diagnostics.Debug.WriteLine($"Failed to log business operation: {ex.Message}");
        }
    }
}

/// <summary>
/// Base class for command handlers that return a value and include audit logging capabilities
/// </summary>
/// <typeparam name="TCommand">The type of command being handled</typeparam>
/// <typeparam name="TResponse">The type of response returned</typeparam>
public abstract class BaseAuditableCommandHandler<TCommand, TResponse> : IRequestHandler<TCommand, TResponse>
    where TCommand : BaseCommand<TResponse>
{
    protected readonly IAuditLogService AuditLogService;

    protected BaseAuditableCommandHandler(IAuditLogService auditLogService)
    {
        AuditLogService = auditLogService;
    }

    public abstract Task<TResponse> Handle(TCommand request, CancellationToken cancellationToken);

    /// <summary>
    /// Logs a business operation for audit purposes
    /// </summary>
    /// <param name="actionType">Type of business action performed</param>
    /// <param name="entityType">Type of entity involved</param>
    /// <param name="entityId">ID of the entity involved</param>
    /// <param name="details">Additional context or details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected async Task LogBusinessOperationAsync(
        string actionType,
        string entityType,
        string entityId,
        string? details = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await AuditLogService.LogBusinessOperationAsync(
                actionType, entityType, entityId, details, cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the main operation
            // This could be enhanced with proper logging
            System.Diagnostics.Debug.WriteLine($"Failed to log business operation: {ex.Message}");
        }
    }
}
