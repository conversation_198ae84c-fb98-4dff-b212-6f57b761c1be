using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;
using HWSAuditPlatform.Domain.Entities.Workflow;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Tests.Common;
using HWSAuditPlatform.Infrastructure.Services;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Tests.Infrastructure.Services;

public class AuditLogServiceTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;
    private readonly Mock<ILogger<AuditLogService>> _loggerMock;
    private readonly AuditLogService _auditLogService;

    public AuditLogServiceTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        _context = new ApplicationDbContext(options, new TestCurrentUserService());

        // Setup mocks
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        _loggerMock = new Mock<ILogger<AuditLogService>>();

        // Setup HTTP context with user claims
        var httpContext = new DefaultHttpContext();
        httpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new[]
        {
            new Claim("sub", "test-user-guid-12345678901234567890"),
            new Claim(ClaimTypes.Name, "testuser")
        }));
        httpContext.Connection.RemoteIpAddress = System.Net.IPAddress.Parse("*************");

        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContext);

        _auditLogService = new AuditLogService(_context, new TestCurrentUserService(), _httpContextAccessorMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task LogEntityCreatedAsync_ShouldCreateAuditLogEntry()
    {
        // Arrange
        var entityType = "User";
        var entityId = "test-entity-id";
        var newValues = "{\"Name\":\"Test User\",\"Email\":\"<EMAIL>\"}";

        // Act
        await _auditLogService.LogEntityCreatedAsync(entityType, entityId, newValues);

        // Assert
        var auditLog = await _context.AuditLogs.FirstOrDefaultAsync();
        Assert.NotNull(auditLog);
        Assert.Equal("Create", auditLog.ActionType);
        Assert.Equal(entityType, auditLog.EntityType);
        Assert.Equal(entityId, auditLog.EntityId);
        Assert.Equal("test-user-id", auditLog.UserId);
        Assert.Contains("Test User", auditLog.NewValues);
        Assert.Contains("<EMAIL>", auditLog.NewValues);
        Assert.Null(auditLog.OldValues);
        Assert.Equal("*************", auditLog.IPAddress);
        Assert.NotNull(auditLog.AppVersion);
    }

    [Fact]
    public async Task LogEntityUpdatedAsync_ShouldCreateAuditLogEntryWithOldAndNewValues()
    {
        // Arrange
        var entityType = "User";
        var entityId = "test-entity-id";
        var oldValues = "{\"Name\":\"Old Name\",\"Email\":\"<EMAIL>\"}";
        var newValues = "{\"Name\":\"New Name\",\"Email\":\"<EMAIL>\"}";

        // Act
        await _auditLogService.LogEntityUpdatedAsync(entityType, entityId, oldValues, newValues);

        // Assert
        var auditLog = await _context.AuditLogs.FirstOrDefaultAsync();
        Assert.NotNull(auditLog);
        Assert.Equal("Update", auditLog.ActionType);
        Assert.Equal(entityType, auditLog.EntityType);
        Assert.Equal(entityId, auditLog.EntityId);
        Assert.Contains("Old Name", auditLog.OldValues);
        Assert.Contains("New Name", auditLog.NewValues);
    }

    [Fact]
    public async Task LogEntityDeletedAsync_ShouldCreateAuditLogEntry()
    {
        // Arrange
        var entityType = "User";
        var entityId = "test-entity-id";
        var oldValues = "{\"Name\":\"Deleted User\",\"Email\":\"<EMAIL>\"}";

        // Act
        await _auditLogService.LogEntityDeletedAsync(entityType, entityId, oldValues);

        // Assert
        var auditLog = await _context.AuditLogs.FirstOrDefaultAsync();
        Assert.NotNull(auditLog);
        Assert.Equal("Delete", auditLog.ActionType);
        Assert.Equal(entityType, auditLog.EntityType);
        Assert.Equal(entityId, auditLog.EntityId);
        Assert.Contains("Deleted User", auditLog.OldValues);
        Assert.Null(auditLog.NewValues);
    }

    [Fact]
    public async Task LogBusinessOperationAsync_ShouldCreateAuditLogEntry()
    {
        // Arrange
        var operationType = "PublishTemplate";
        var entityType = "AuditTemplate";
        var entityId = "template-123";
        var details = "Template published successfully";

        // Act
        await _auditLogService.LogBusinessOperationAsync(operationType, entityType, entityId, details);

        // Assert
        var auditLog = await _context.AuditLogs.FirstOrDefaultAsync();
        Assert.NotNull(auditLog);
        Assert.Equal(operationType, auditLog.ActionType);
        Assert.Equal(entityType, auditLog.EntityType);
        Assert.Equal(entityId, auditLog.EntityId);
        Assert.Equal(details, auditLog.Details);
    }

    [Fact]
    public async Task LogAuthenticationEventAsync_ShouldCreateAuditLogEntry()
    {
        // Arrange
        var eventType = "LoginSuccess";
        var username = "testuser";
        var details = "Successful login with role Admin";

        // Act
        await _auditLogService.LogAuthenticationEventAsync(eventType, username, details);

        // Assert
        var auditLog = await _context.AuditLogs.FirstOrDefaultAsync();
        Assert.NotNull(auditLog);
        Assert.Equal(eventType, auditLog.ActionType);
        Assert.Equal("Authentication", auditLog.EntityType);
        Assert.Equal(username, auditLog.EntityId);
        Assert.Equal(details, auditLog.Details);
    }

    [Fact]
    public async Task LogSystemEventAsync_ShouldCreateAuditLogEntry()
    {
        // Arrange
        var eventType = "SystemStartup";
        var details = "Application started successfully";

        // Act
        await _auditLogService.LogSystemEventAsync(eventType, details);

        // Assert
        var auditLog = await _context.AuditLogs.FirstOrDefaultAsync();
        Assert.NotNull(auditLog);
        Assert.Equal(eventType, auditLog.ActionType);
        Assert.Equal("System", auditLog.EntityType);
        Assert.Equal("System", auditLog.EntityId);
        Assert.Equal(details, auditLog.Details);
    }

    [Fact]
    public async Task LogEntityCreatedAsync_WithoutHttpContext_ShouldStillCreateAuditLogEntry()
    {
        // Arrange - Create a service with a mock that returns null for UserId
        var nullUserServiceMock = new Mock<ICurrentUserService>();
        nullUserServiceMock.Setup(x => x.UserId).Returns((string?)null);
        nullUserServiceMock.Setup(x => x.Username).Returns((string?)null);
        nullUserServiceMock.Setup(x => x.IsAuthenticated).Returns(false);

        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns((HttpContext?)null);

        var serviceWithNullUser = new AuditLogService(_context, nullUserServiceMock.Object, _httpContextAccessorMock.Object, _loggerMock.Object);

        var entityType = "User";
        var entityId = "test-entity-id";
        var newValues = "{\"Name\":\"Test User\"}";

        // Act
        await serviceWithNullUser.LogEntityCreatedAsync(entityType, entityId, newValues);

        // Assert
        var auditLog = await _context.AuditLogs.FirstOrDefaultAsync();
        Assert.NotNull(auditLog);
        Assert.Equal("System", auditLog.UserId); // Should default to "System"
        Assert.Null(auditLog.IPAddress); // Should be null when no HTTP context
    }

    [Fact]
    public async Task LogEntityCreatedAsync_WithException_ShouldLogErrorAndNotThrow()
    {
        // Arrange - Create a service with an invalid connection string to simulate database error
        var invalidOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: "invalid-db")
            .Options;

        var invalidContext = new ApplicationDbContext(invalidOptions, new TestCurrentUserService());
        // Dispose the context immediately to simulate a disposed context
        invalidContext.Dispose();

        var serviceWithInvalidContext = new AuditLogService(invalidContext, new TestCurrentUserService(), _httpContextAccessorMock.Object, _loggerMock.Object);

        // Act & Assert - Should not throw even when context is disposed
        await serviceWithInvalidContext.LogEntityCreatedAsync("User", "test-id", "{\"Name\":\"Test\"}");

        // Verify error was logged
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error creating audit log for")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
