using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Users;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Commands.CreateUser;

/// <summary>
/// Handler for CreateUserCommand
/// </summary>
public class CreateUserCommandHandler : BaseAuditableCommandHandler<CreateUserCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateUserCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService) : base(auditLogService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<string> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // Check for duplicate username
        var existingUserByUsername = await _context.Users
            .FirstOrDefaultAsync(u => u.Username == request.Username, cancellationToken);

        if (existingUserByUsername != null)
        {
            throw new InvalidOperationException($"A user with username '{request.Username}' already exists");
        }

        // Check for duplicate email
        var existingUserByEmail = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == request.Email, cancellationToken);

        if (existingUserByEmail != null)
        {
            throw new InvalidOperationException($"A user with email '{request.Email}' already exists");
        }

        // Get the role entity
        var role = await _context.Roles
            .FirstOrDefaultAsync(r => r.RoleName == request.Role, cancellationToken);

        if (role == null)
        {
            throw new InvalidOperationException($"Role '{request.Role}' not found");
        }

        // Create the user entity
        var user = User.Create(
            username: request.Username,
            firstName: request.FirstName,
            lastName: request.LastName,
            email: request.Email,
            roleId: role.Id,
            factoryId: request.FactoryId,
            isActive: request.IsActive,
            adObjectGuid: request.AdObjectGuid,
            adDistinguishedName: request.AdDistinguishedName,
            createdByUserId: _currentUserService.UserId);

        // Add to context
        await _context.Users.AddAsync(user, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        // Log the business operation
        await LogBusinessOperationAsync(
            "UserCreated",
            "User",
            user.Id,
            $"Created user '{user.Username}' with role '{request.Role}' in factory {request.FactoryId}",
            cancellationToken);

        return user.Id;
    }
}
