@page "/corrective-actions/{ActionId}"
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.Domain.Enums
@inject ICorrectiveActionApiService CorrectiveActionApiService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@attribute [Authorize]

<PageTitle>Corrective Action Details - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading corrective action details...</p>
        </div>
    }
    else if (action == null)
    {
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h4>Corrective Action Not Found</h4>
            <p class="text-muted">The requested corrective action could not be found.</p>
            <button class="btn btn-primary" @onclick="NavigateToCorrectiveActions">
                <i class="fas fa-arrow-left"></i> Back to Corrective Actions
            </button>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="/corrective-actions" class="text-decoration-none">Corrective Actions</a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    Action #@action.Id.Substring(0, 8)
                                </li>
                            </ol>
                        </nav>
                        <h2 class="mb-1">Corrective Action Details</h2>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" @onclick="RefreshData">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i> Actions
                            </button>
                            <ul class="dropdown-menu">
                                @if (CanUpdateStatus())
                                {
                                    <li><a class="dropdown-item" href="#" @onclick="ShowStatusModal">
                                        <i class="fas fa-edit me-2"></i>Update Status
                                    </a></li>
                                }
                                @if (CanComplete())
                                {
                                    <li><a class="dropdown-item" href="#" @onclick="ShowCompleteModal">
                                        <i class="fas fa-check me-2"></i>Mark Complete
                                    </a></li>
                                }
                                @if (CanVerify())
                                {
                                    <li><a class="dropdown-item" href="#" @onclick="ShowVerifyModal">
                                        <i class="fas fa-check-double me-2"></i>Verify Action
                                    </a></li>
                                }
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/findings/@action.FindingId">
                                    <i class="fas fa-exclamation-triangle me-2"></i>View Related Finding
                                </a></li>
                                <li><a class="dropdown-item" href="/audits/@action.AuditId">
                                    <i class="fas fa-clipboard-check me-2"></i>View Related Audit
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Action Overview Card -->
                <div class="card border-0 shadow-sm mb-4 @GetActionCardClass()">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                Action #@action.Id.Substring(0, 8)
                            </h5>
                            <div class="d-flex gap-2">
                                <span class="badge @GetStatusBadgeClass() fs-6">@action.Status</span>
                                @if (action.IsOverdue)
                                {
                                    <span class="badge bg-danger fs-6">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Overdue
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="text-muted mb-2">Action Description</h6>
                                <p class="mb-3">@action.ActionDescription</p>
                                
                                @if (!string.IsNullOrEmpty(action.ActionNotes))
                                {
                                    <h6 class="text-muted mb-2">Action Notes</h6>
                                    <p class="mb-3">@action.ActionNotes</p>
                                }
                            </div>
                            <div class="col-md-4">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-1">Due Date</h6>
                                        <p class="mb-0 @(action.IsOverdue ? "text-danger fw-bold" : "")">
                                            @action.DueDate.ToString("MMM dd, yyyy")
                                            @if (action.IsOverdue)
                                            {
                                                <i class="fas fa-exclamation-triangle ms-1"></i>
                                            }
                                        </p>
                                    </div>
                                    
                                    <div class="col-12">
                                        <h6 class="text-muted mb-1">Created</h6>
                                        <p class="mb-0">@action.CreatedAt.ToString("MMM dd, yyyy HH:mm")</p>
                                    </div>
                                    
                                    @if (action.CompletedAt.HasValue)
                                    {
                                        <div class="col-12">
                                            <h6 class="text-muted mb-1">Completed</h6>
                                            <p class="mb-0">@action.CompletedAt.Value.ToString("MMM dd, yyyy HH:mm")</p>
                                        </div>
                                    }
                                    
                                    @if (action.VerifiedAt.HasValue)
                                    {
                                        <div class="col-12">
                                            <h6 class="text-muted mb-1">Verified</h6>
                                            <p class="mb-0">@action.VerifiedAt.Value.ToString("MMM dd, yyyy HH:mm")</p>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Finding Card -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="card-title mb-0">Related Finding</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-2">
                                    <a href="/findings/@action.FindingId" class="text-decoration-none">
                                        @(action.FindingCode ?? $"Finding #{action.FindingId.Substring(0, 8)}")
                                    </a>
                                    <span class="badge @GetFindingSeverityBadgeClass() ms-2">@action.FindingSeverityLevel</span>
                                </h6>
                                <p class="text-muted mb-0">@action.FindingDescription</p>
                            </div>
                            <div class="col-md-4">
                                <div class="text-end">
                                    <p class="mb-1"><strong>Audit:</strong> @action.AuditTitle</p>
                                    <p class="mb-1"><strong>Area:</strong> @action.AreaName</p>
                                    <p class="mb-0"><strong>Factory:</strong> @action.FactoryName</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assignment and Progress Card -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="card-title mb-0">Assignment & Progress</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted mb-2">Assigned To</h6>
                                @if (!string.IsNullOrEmpty(action.AssignedUserFullName))
                                {
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-user-check text-success me-2"></i>
                                        <span>@action.AssignedUserFullName</span>
                                    </div>
                                }
                                else
                                {
                                    <div class="d-flex align-items-center text-muted mb-3">
                                        <i class="fas fa-user-slash me-2"></i>
                                        <span>Not assigned</span>
                                    </div>
                                }

                                @if (!string.IsNullOrEmpty(action.AssignedByUserFullName))
                                {
                                    <h6 class="text-muted mb-2">Assigned By</h6>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user me-2"></i>
                                        <span>@action.AssignedByUserFullName</span>
                                    </div>
                                }
                            </div>
                            <div class="col-md-6">
                                @if (!string.IsNullOrEmpty(action.VerifiedByUserFullName))
                                {
                                    <h6 class="text-muted mb-2">Verified By</h6>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-check-double text-success me-2"></i>
                                        <span>@action.VerifiedByUserFullName</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Completion Details Card -->
                @if (action.IsCompleted || !string.IsNullOrEmpty(action.CompletionNotes))
                {
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white border-bottom">
                            <h5 class="card-title mb-0">Completion Details</h5>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(action.CompletionNotes))
                            {
                                <h6 class="text-muted mb-2">Completion Notes</h6>
                                <p class="mb-3">@action.CompletionNotes</p>
                            }
                            
                            @if (!string.IsNullOrEmpty(action.VerificationNotes))
                            {
                                <h6 class="text-muted mb-2">Verification Notes</h6>
                                <p class="mb-3">@action.VerificationNotes</p>
                                
                                @if (action.IsEffective.HasValue)
                                {
                                    <div class="alert @(action.IsEffective.Value ? "alert-success" : "alert-warning")">
                                        <i class="fas @(action.IsEffective.Value ? "fa-check-circle" : "fa-exclamation-triangle") me-2"></i>
                                        This corrective action has been verified as <strong>@(action.IsEffective.Value ? "Effective" : "Ineffective")</strong>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public string ActionId { get; set; } = string.Empty;

    private bool isLoading = true;
    private CorrectiveActionDto? action;

    protected override async Task OnInitializedAsync()
    {
        await LoadAction();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(ActionId))
        {
            await LoadAction();
        }
    }

    private async Task LoadAction()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            action = await CorrectiveActionApiService.GetCorrectiveActionAsync(ActionId);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", "Error loading corrective action:", ex.Message);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadAction();
    }

    private void ShowStatusModal()
    {
        // TODO: Implement status update modal
        JSRuntime.InvokeVoidAsync("alert", "Status update functionality will be implemented soon.");
    }

    private void ShowCompleteModal()
    {
        // TODO: Implement complete action modal
        JSRuntime.InvokeVoidAsync("alert", "Complete action functionality will be implemented soon.");
    }

    private void ShowVerifyModal()
    {
        // TODO: Implement verify action modal
        JSRuntime.InvokeVoidAsync("alert", "Verify action functionality will be implemented soon.");
    }

    private string GetActionCardClass()
    {
        if (action == null) return "";
        return action.IsOverdue ? "border-danger" : "";
    }

    private string GetStatusBadgeClass()
    {
        if (action == null) return "bg-light";
        
        return action.Status switch
        {
            CorrectiveActionStatus.Assigned => "bg-info",
            CorrectiveActionStatus.InProgress => "bg-warning",
            CorrectiveActionStatus.CompletedPendingVerification => "bg-primary",
            CorrectiveActionStatus.VerifiedClosed => "bg-success",
            CorrectiveActionStatus.Cancelled => "bg-secondary",
            CorrectiveActionStatus.Ineffective => "bg-danger",
            _ => "bg-light"
        };
    }

    private string GetFindingSeverityBadgeClass()
    {
        if (action == null) return "bg-light";
        
        return action.FindingSeverityLevel.ToString().ToLower() switch
        {
            "critical" => "bg-danger",
            "major" => "bg-warning",
            "minor" => "bg-info",
            "observation" => "bg-secondary",
            _ => "bg-light"
        };
    }

    private bool CanUpdateStatus()
    {
        return action != null && 
               action.Status != CorrectiveActionStatus.VerifiedClosed && 
               action.Status != CorrectiveActionStatus.Cancelled;
    }

    private bool CanComplete()
    {
        return action != null && action.Status == CorrectiveActionStatus.InProgress;
    }

    private bool CanVerify()
    {
        return action != null && action.Status == CorrectiveActionStatus.CompletedPendingVerification;
    }

    private void NavigateToCorrectiveActions()
    {
        Navigation.NavigateTo("/corrective-actions");
    }
}
