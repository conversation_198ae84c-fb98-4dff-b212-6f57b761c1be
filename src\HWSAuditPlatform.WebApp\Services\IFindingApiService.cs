using HWSAuditPlatform.WebApp.Models;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Interface for Finding API operations
/// </summary>
public interface IFindingApiService
{
    /// <summary>
    /// Gets findings with filtering and pagination
    /// </summary>
    Task<PagedResult<FindingDto>?> GetFindingsAsync(
        string? searchTerm = null,
        SeverityLevel? severityLevel = null,
        FindingStatus? status = null,
        int? categoryId = null,
        bool? isOverdue = null,
        bool? isResolved = null,
        string? responsibleUserId = null,
        int? factoryId = null,
        int? areaId = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        int pageNumber = 1,
        int pageSize = 20,
        string sortBy = "CreatedAt",
        string sortDirection = "desc");

    /// <summary>
    /// Gets a specific finding by ID
    /// </summary>
    Task<FindingDto?> GetFindingAsync(string id, bool includeCorrectiveActions = false);

    /// <summary>
    /// Creates a new finding
    /// </summary>
    Task<FindingDto?> CreateFindingAsync(CreateFindingRequest request);

    /// <summary>
    /// Creates a finding from an audit answer
    /// </summary>
    Task<FindingDto?> CreateFindingFromAuditAnswerAsync(string auditAnswerId, bool forceCreation = false);

    /// <summary>
    /// Updates finding status
    /// </summary>
    Task<bool> UpdateFindingStatusAsync(string id, FindingStatus status, string? statusChangeNotes = null);

    /// <summary>
    /// Gets findings for a specific audit
    /// </summary>
    Task<List<FindingDto>?> GetAuditFindingsAsync(string auditId);

    /// <summary>
    /// Gets overdue findings
    /// </summary>
    Task<PagedResult<FindingDto>?> GetOverdueFindingsAsync(
        string? responsibleUserId = null,
        int pageNumber = 1,
        int pageSize = 20);

    /// <summary>
    /// Gets finding statistics
    /// </summary>
    Task<FindingStatisticsModel?> GetFindingStatisticsAsync(
        int? factoryId = null,
        int? areaId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null);

    /// <summary>
    /// Assigns a category to a finding
    /// </summary>
    Task<bool> AssignFindingCategoryAsync(string findingId, int? categoryId, string? reason = null);

    /// <summary>
    /// Assigns responsibility for a finding
    /// </summary>
    Task<bool> AssignFindingResponsibilityAsync(string findingId, string? responsibleUserId, string? retrospectiveAnalystUserId = null);
}

/// <summary>
/// Request model for creating a finding
/// </summary>
public class CreateFindingRequest
{
    public string AuditAnswerId { get; set; } = string.Empty;
    public string FindingDescription { get; set; } = string.Empty;
    public SeverityLevel FindingSeverityLevel { get; set; }
    public string? RootCauseAnalysis { get; set; }
    public string? ImmediateActionTaken { get; set; }
    public DateTime DueDate { get; set; }
    public int? FindingCategoryId { get; set; }
}

/// <summary>
/// Request model for updating finding status
/// </summary>
public class UpdateFindingStatusRequest
{
    public FindingStatus Status { get; set; }
    public string? StatusChangeNotes { get; set; }
}

/// <summary>
/// Request model for assigning finding responsibility
/// </summary>
public class AssignFindingResponsibilityRequest
{
    public string? ResponsibleUserId { get; set; }
    public string? RetrospectiveAnalystUserId { get; set; }
}
