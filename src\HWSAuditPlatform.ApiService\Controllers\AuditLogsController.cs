using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.AuditLogs.DTOs;
using HWSAuditPlatform.Application.AuditLogs.Queries.GetAuditLogs;
using HWSAuditPlatform.Application.AuditLogs.Queries.GetAuditLog;
using HWSAuditPlatform.ApiService.Models;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for audit log operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[Authorize]
public class AuditLogsController : BaseController
{
    public AuditLogsController(IMediator mediator, ILogger<AuditLogsController> logger)
        : base(mediator, logger)
    {
    }

    /// <summary>
    /// Get audit logs with filtering and pagination
    /// </summary>
    /// <param name="filter">Filter criteria</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audit logs</returns>
    [HttpGet]
    [Authorize(Roles = "DevAdmin,SystemManager")]
    [ProducesResponseType(typeof(PaginatedResult<AuditLogDto>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 401)]
    [ProducesResponseType(typeof(ApiErrorResponse), 403)]
    public async Task<ActionResult<PaginatedResult<AuditLogDto>>> GetAuditLogs(
        [FromQuery] AuditLogFilterDto filter,
        CancellationToken cancellationToken)
    {
        try
        {
            Logger.LogInformation("Getting audit logs with filter: {@Filter}", filter);

            var query = new GetAuditLogsQuery { Filter = filter };
            var result = await Mediator.Send(query, cancellationToken);

            Logger.LogInformation("Retrieved {Count} audit logs out of {Total} total", 
                result.Items.Count, result.TotalCount);

            return Success(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving audit logs");
            return StatusCode(500, new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Internal Server Error",
                Detail = "An error occurred while retrieving audit logs"
            });
        }
    }

    /// <summary>
    /// Get a specific audit log by ID
    /// </summary>
    /// <param name="id">Audit log ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Audit log details</returns>
    [HttpGet("{id:int}")]
    [Authorize(Roles = "DevAdmin,SystemManager")]
    [ProducesResponseType(typeof(AuditLogDto), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 401)]
    [ProducesResponseType(typeof(ApiErrorResponse), 403)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<AuditLogDto>> GetAuditLog(
        int id,
        CancellationToken cancellationToken)
    {
        try
        {
            Logger.LogInformation("Getting audit log with ID: {Id}", id);

            var query = new GetAuditLogQuery { Id = id };
            var result = await Mediator.Send(query, cancellationToken);

            if (result == null)
            {
                Logger.LogWarning("Audit log with ID {Id} not found", id);
                return NotFound(new ApiErrorResponse
                {
                    StatusCode = 404,
                    Title = "Audit Log Not Found",
                    Detail = $"Audit log with ID {id} was not found"
                });
            }

            Logger.LogInformation("Retrieved audit log with ID: {Id}", id);
            return Success(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving audit log with ID: {Id}", id);
            return StatusCode(500, new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Internal Server Error",
                Detail = "An error occurred while retrieving the audit log"
            });
        }
    }

    /// <summary>
    /// Get audit logs for a specific entity
    /// </summary>
    /// <param name="entityType">Type of entity</param>
    /// <param name="entityId">ID of the entity</param>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 20)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audit logs for the entity</returns>
    [HttpGet("entity/{entityType}/{entityId}")]
    [Authorize(Roles = "DevAdmin,SystemManager,ProcessOwner")]
    [ProducesResponseType(typeof(PaginatedResult<AuditLogDto>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 401)]
    [ProducesResponseType(typeof(ApiErrorResponse), 403)]
    public async Task<ActionResult<PaginatedResult<AuditLogDto>>> GetAuditLogsForEntity(
        string entityType,
        string entityId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Getting audit logs for entity {EntityType}:{EntityId}", entityType, entityId);

            var filter = new AuditLogFilterDto
            {
                EntityType = entityType,
                EntityId = entityId,
                PageNumber = pageNumber,
                PageSize = Math.Min(pageSize, 100), // Limit page size to 100
                SortBy = "EventTimestamp",
                SortDirection = "desc"
            };

            var query = new GetAuditLogsQuery { Filter = filter };
            var result = await Mediator.Send(query, cancellationToken);

            Logger.LogInformation("Retrieved {Count} audit logs for entity {EntityType}:{EntityId}", 
                result.Items.Count, entityType, entityId);

            return Success(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving audit logs for entity {EntityType}:{EntityId}", entityType, entityId);
            return StatusCode(500, new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Internal Server Error",
                Detail = "An error occurred while retrieving audit logs for the entity"
            });
        }
    }

    /// <summary>
    /// Get audit logs for a specific user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 20)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audit logs for the user</returns>
    [HttpGet("user/{userId}")]
    [Authorize(Roles = "DevAdmin,SystemManager")]
    [ProducesResponseType(typeof(PaginatedResult<AuditLogDto>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 401)]
    [ProducesResponseType(typeof(ApiErrorResponse), 403)]
    public async Task<ActionResult<PaginatedResult<AuditLogDto>>> GetAuditLogsForUser(
        string userId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Getting audit logs for user: {UserId}", userId);

            var filter = new AuditLogFilterDto
            {
                UserId = userId,
                PageNumber = pageNumber,
                PageSize = Math.Min(pageSize, 100), // Limit page size to 100
                SortBy = "EventTimestamp",
                SortDirection = "desc"
            };

            var query = new GetAuditLogsQuery { Filter = filter };
            var result = await Mediator.Send(query, cancellationToken);

            Logger.LogInformation("Retrieved {Count} audit logs for user: {UserId}", 
                result.Items.Count, userId);

            return Success(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving audit logs for user: {UserId}", userId);
            return StatusCode(500, new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Internal Server Error",
                Detail = "An error occurred while retrieving audit logs for the user"
            });
        }
    }
}
