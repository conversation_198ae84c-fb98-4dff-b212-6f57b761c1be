using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.AuditLogs.DTOs;

namespace HWSAuditPlatform.Application.AuditLogs.Queries.GetAuditLog;

/// <summary>
/// Handler for GetAuditLogQuery
/// </summary>
public class GetAuditLogQueryHandler : BaseQueryHandler<GetAuditLogQuery, AuditLogDto?>
{
    private readonly IApplicationDbContext _context;

    public GetAuditLogQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<AuditLogDto?> Handle(GetAuditLogQuery request, CancellationToken cancellationToken)
    {
        var auditLog = await _context.AuditLogs
            .Include(al => al.User)
            .Where(al => al.Id == request.Id)
            .Select(al => new AuditLogDto
            {
                Id = (int)al.Id,
                EventTimestamp = al.EventTimestamp,
                ServerReceivedAt = al.ServerReceivedAt,
                UserId = al.UserId,
                Username = al.User != null ? al.User.Username : "Unknown",
                UserFullName = al.User != null ? $"{al.User.FirstName} {al.User.LastName}".Trim() : "Unknown",
                EntityType = al.EntityType,
                EntityId = al.EntityId,
                ActionType = al.ActionType,
                OldValues = al.OldValues,
                NewValues = al.NewValues,
                Details = al.Details,
                IPAddress = al.IPAddress,
                AppVersion = al.AppVersion
            })
            .FirstOrDefaultAsync(cancellationToken);

        return auditLog;
    }
}
