using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Findings.Commands.UpdateFindingStatus;

/// <summary>
/// Command to update the status of a finding
/// </summary>
public class UpdateFindingStatusCommand : BaseCommand<bool>
{
    /// <summary>
    /// The ID of the finding to update
    /// </summary>
    public string FindingId { get; set; } = string.Empty;

    /// <summary>
    /// The new status for the finding
    /// </summary>
    public FindingStatus Status { get; set; }

    /// <summary>
    /// Optional notes about the status change
    /// </summary>
    public string? StatusChangeNotes { get; set; }
}
