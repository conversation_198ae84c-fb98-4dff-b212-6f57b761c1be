@page "/findings/dashboard"
@using HWSAuditPlatform.WebApp.Components.Dashboard
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@inject IConfiguration Configuration
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>Findings Dashboard - HWS Audit Platform</PageTitle>

@if (skipAuthentication)
{
    <!-- Debug mode: Skip authorization check -->
    <FindingsDashboardComponent />
}
else
{
    <!-- Normal mode: Require authorization -->
    <AuthorizeView>
        <Authorized>
            <FindingsDashboardComponent />
        </Authorized>
        <NotAuthorized>
            <div class="text-center p-4">
                <h3>Access Denied</h3>
                <p>You need to be logged in to access the findings dashboard.</p>
                <a href="/login" class="btn btn-primary">Go to Login</a>
            </div>
        </NotAuthorized>
    </AuthorizeView>
}

@code {
    private bool skipAuthentication = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if we're in debug mode
        var debugMode = Configuration.GetValue<bool>("DebugMode");
        if (debugMode)
        {
            skipAuthentication = true;
        }
        else
        {
            // Check authentication state
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            skipAuthentication = !authState.User.Identity?.IsAuthenticated ?? true;
        }
    }
}
