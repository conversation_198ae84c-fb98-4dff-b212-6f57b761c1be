namespace HWSAuditPlatform.Application.AuditLogs.DTOs;

/// <summary>
/// Data transfer object for audit log entries
/// </summary>
public class AuditLogDto
{
    /// <summary>
    /// Unique identifier for the audit log entry
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Timestamp when the event occurred
    /// </summary>
    public DateTime EventTimestamp { get; set; }

    /// <summary>
    /// Timestamp when the server received the event
    /// </summary>
    public DateTime ServerReceivedAt { get; set; }

    /// <summary>
    /// ID of the user who performed the action
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Username of the user who performed the action
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Full name of the user who performed the action
    /// </summary>
    public string UserFullName { get; set; } = string.Empty;

    /// <summary>
    /// Type of entity that was affected
    /// </summary>
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// ID of the entity that was affected
    /// </summary>
    public string EntityId { get; set; } = string.Empty;

    /// <summary>
    /// Type of action performed (Create, Update, Delete, etc.)
    /// </summary>
    public string ActionType { get; set; } = string.Empty;

    /// <summary>
    /// JSON representation of the entity values before the change
    /// </summary>
    public string? OldValues { get; set; }

    /// <summary>
    /// JSON representation of the entity values after the change
    /// </summary>
    public string? NewValues { get; set; }

    /// <summary>
    /// Additional details about the action
    /// </summary>
    public string? Details { get; set; }

    /// <summary>
    /// IP address from which the action was performed
    /// </summary>
    public string? IPAddress { get; set; }

    /// <summary>
    /// Version of the application when the action was performed
    /// </summary>
    public string? AppVersion { get; set; }
}

/// <summary>
/// Audit log filter criteria for querying
/// </summary>
public class AuditLogFilterDto
{
    /// <summary>
    /// Filter by user ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// Filter by username (partial match)
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// Filter by entity type
    /// </summary>
    public string? EntityType { get; set; }

    /// <summary>
    /// Filter by entity ID
    /// </summary>
    public string? EntityId { get; set; }

    /// <summary>
    /// Filter by action type
    /// </summary>
    public string? ActionType { get; set; }

    /// <summary>
    /// Filter by events from this date (inclusive)
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// Filter by events to this date (inclusive)
    /// </summary>
    public DateTime? ToDate { get; set; }

    /// <summary>
    /// Filter by IP address
    /// </summary>
    public string? IPAddress { get; set; }

    /// <summary>
    /// Search term for details field
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Page number for pagination (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// Sort field (EventTimestamp, Username, EntityType, ActionType)
    /// </summary>
    public string SortBy { get; set; } = "EventTimestamp";

    /// <summary>
    /// Sort direction (asc, desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}
