@using HWSAuditPlatform.WebAuditPWA.Models

<div class="immediate-action-container">
    <div class="action-header">
        <h6 class="action-title">
            <i class="fas fa-bolt me-2"></i>
            Immediate Action Taken
        </h6>
        <div class="action-toggle">
            <label class="toggle-switch">
                <input type="checkbox" checked="@hasAction" @onchange="OnActionToggleChanged" />
                <span class="toggle-slider"></span>
            </label>
            <span class="toggle-label">@(hasAction ? "Action Taken" : "No Action")</span>
        </div>
    </div>

    @if (hasAction)
    {
        <div class="action-input-section">
            <div class="action-field">
                <label class="action-label">
                    <i class="fas fa-clipboard-check me-2"></i>
                    Action Description
                </label>
                <textarea class="action-textarea"
                          rows="3"
                          placeholder="Describe the immediate corrective action taken on-site..."
                          value="@actionDescription"
                          @onchange="OnActionDescriptionChanged">
                </textarea>
            </div>

            <div class="action-field">
                <label class="action-label">
                    <i class="fas fa-user me-2"></i>
                    Action Taken By
                </label>
                <input type="text" 
                       class="action-input"
                       placeholder="Name of person who took action"
                       value="@actionTakenBy"
                       @onchange="OnActionTakenByChanged" />
            </div>

            <div class="action-field">
                <label class="action-label">
                    <i class="fas fa-clock me-2"></i>
                    Time of Action
                </label>
                <input type="datetime-local" 
                       class="action-input"
                       value="@actionTime"
                       @onchange="OnActionTimeChanged" />
            </div>

            <div class="action-effectiveness">
                <label class="action-label">
                    <i class="fas fa-check-circle me-2"></i>
                    Effectiveness Assessment
                </label>
                <div class="effectiveness-options">
                    <label class="effectiveness-option">
                        <input type="radio" name="effectiveness" value="temporary" @onchange="@(() => OnEffectivenessChanged("temporary"))" />
                        <span class="effectiveness-text">
                            <i class="fas fa-clock text-warning me-2"></i>
                            Temporary Fix
                        </span>
                    </label>
                    <label class="effectiveness-option">
                        <input type="radio" name="effectiveness" value="permanent" @onchange="@(() => OnEffectivenessChanged("permanent"))" />
                        <span class="effectiveness-text">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Permanent Solution
                        </span>
                    </label>
                    <label class="effectiveness-option">
                        <input type="radio" name="effectiveness" value="unknown" @onchange="@(() => OnEffectivenessChanged("unknown"))" />
                        <span class="effectiveness-text">
                            <i class="fas fa-question-circle text-muted me-2"></i>
                            Effectiveness Unknown
                        </span>
                    </label>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="no-action-message">
            <i class="fas fa-info-circle me-2"></i>
            No immediate action was taken at the time of audit
        </div>
    }
</div>

@code {
    [Parameter] public ImmediateActionModel? Action { get; set; }
    [Parameter] public EventCallback<ImmediateActionModel?> ActionChanged { get; set; }

    private bool hasAction = false;
    private string actionDescription = "";
    private string actionTakenBy = "";
    private DateTime? actionTime;
    private string effectiveness = "unknown";

    protected override void OnParametersSet()
    {
        if (Action != null)
        {
            hasAction = true;
            actionDescription = Action.Description ?? "";
            actionTakenBy = Action.TakenBy ?? "";
            actionTime = Action.ActionTime;
            effectiveness = Action.Effectiveness ?? "unknown";
        }
        else
        {
            hasAction = false;
            actionDescription = "";
            actionTakenBy = "";
            actionTime = null;
            effectiveness = "unknown";
        }
    }

    private async Task OnActionToggleChanged(ChangeEventArgs e)
    {
        hasAction = (bool)(e.Value ?? false);

        if (!hasAction)
        {
            // Clear all action data when toggled off
            actionDescription = "";
            actionTakenBy = "";
            actionTime = null;
            effectiveness = "unknown";
        }
        else
        {
            // Set default action time to now when toggled on
            actionTime = DateTime.Now;
        }

        await NotifyActionChanged();
    }

    private async Task OnActionDescriptionChanged(ChangeEventArgs e)
    {
        actionDescription = e.Value?.ToString() ?? "";
        await NotifyActionChanged();
    }

    private async Task OnActionTakenByChanged(ChangeEventArgs e)
    {
        actionTakenBy = e.Value?.ToString() ?? "";
        await NotifyActionChanged();
    }

    private async Task OnActionTimeChanged(ChangeEventArgs e)
    {
        if (DateTime.TryParse(e.Value?.ToString(), out var dateValue))
        {
            actionTime = dateValue;
        }
        await NotifyActionChanged();
    }

    private async Task OnEffectivenessChanged(string newEffectiveness)
    {
        effectiveness = newEffectiveness;
        await NotifyActionChanged();
    }

    private async Task NotifyActionChanged()
    {
        ImmediateActionModel? actionModel = null;

        if (hasAction && !string.IsNullOrWhiteSpace(actionDescription))
        {
            actionModel = new ImmediateActionModel
            {
                Description = actionDescription,
                TakenBy = actionTakenBy,
                ActionTime = actionTime,
                Effectiveness = effectiveness
            };
        }

        if (ActionChanged.HasDelegate)
        {
            await ActionChanged.InvokeAsync(actionModel);
        }
    }
}

<style>
    .immediate-action-container {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--industrial-border-light);
        border-radius: 12px;
        padding: 1.25rem;
        margin: 1rem 0;
    }

    .action-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .action-title {
        display: flex;
        align-items: center;
        color: var(--industrial-teal);
        font-size: 0.95rem;
        font-weight: 700;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .action-toggle {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.3s;
        border-radius: 24px;
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: 0.3s;
        border-radius: 50%;
    }

    input:checked + .toggle-slider {
        background-color: var(--industrial-teal);
    }

    input:checked + .toggle-slider:before {
        transform: translateX(26px);
    }

    .toggle-label {
        color: var(--industrial-text);
        font-size: 0.85rem;
        font-weight: 500;
    }

    .action-input-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .action-field {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-label {
        display: flex;
        align-items: center;
        color: var(--industrial-text);
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .action-textarea, .action-input {
        background-color: var(--industrial-light-gray);
        border: 2px solid var(--industrial-border);
        color: var(--industrial-text);
        border-radius: 6px;
        padding: 0.75rem;
        font-size: 0.9rem;
        font-family: inherit;
        transition: all 0.3s ease;
    }

    .action-textarea {
        resize: vertical;
        min-height: 70px;
    }

    .action-textarea:focus, .action-input:focus {
        background-color: white;
        border-color: var(--industrial-teal);
        color: var(--industrial-dark);
        box-shadow: 0 0 0 0.15rem rgba(20, 184, 166, 0.25);
        outline: none;
    }

    .action-textarea::placeholder, .action-input::placeholder {
        color: var(--industrial-text-muted);
    }

    .action-effectiveness {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .effectiveness-options {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .effectiveness-option {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 6px;
        transition: background-color 0.3s ease;
    }

    .effectiveness-option:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .effectiveness-option input[type="radio"] {
        margin: 0;
    }

    .effectiveness-text {
        display: flex;
        align-items: center;
        color: var(--industrial-text);
        font-size: 0.85rem;
    }

    .no-action-message {
        display: flex;
        align-items: center;
        color: var(--industrial-text-muted);
        font-style: italic;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.02);
        border-radius: 6px;
        font-size: 0.85rem;
    }

    /* Responsive Design */
    @@media (max-width: 767px) {
        .action-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .effectiveness-options {
            gap: 0.75rem;
        }

        .effectiveness-option {
            padding: 0.75rem;
        }
    }

    /* Touch-friendly improvements */
    @@media (hover: none) and (pointer: coarse) {
        .action-textarea, .action-input {
            padding: 1rem;
            font-size: 16px; /* Prevents zoom on iOS */
        }

        .action-textarea {
            min-height: 80px;
        }

        .effectiveness-option {
            padding: 1rem;
            min-height: 48px;
        }

        .toggle-switch {
            width: 60px;
            height: 30px;
        }

        .toggle-slider:before {
            height: 22px;
            width: 22px;
            left: 4px;
            bottom: 4px;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(30px);
        }
    }
</style>
