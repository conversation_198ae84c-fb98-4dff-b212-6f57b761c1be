@page "/corrective-actions"
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebApp.Components.Shared
@inject ICorrectiveActionApiService CorrectiveActionApiService
@inject IUserApiService UserApiService
@inject IJSRuntime JSRuntime
@attribute [Authorize]

<PageTitle>Corrective Actions - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Corrective Actions</h2>
                    <p class="text-muted mb-0">Manage and track corrective actions for audit findings</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" @onclick="RefreshData">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button class="btn btn-primary" @onclick="ShowCreateActionModal">
                        <i class="fas fa-plus"></i> Create Action
                    </button>
                </div>
            </div>

            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading corrective actions...</p>
                </div>
            }
            else
            {
                <!-- Statistics Cards -->
                @if (statistics != null)
                {
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                                <i class="fas fa-tasks text-primary"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="card-title mb-1">Total Actions</h6>
                                            <h4 class="mb-0">@statistics.TotalActions</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                                <i class="fas fa-play text-info"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="card-title mb-1">In Progress</h6>
                                            <h4 class="mb-0">@statistics.InProgressActions</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-danger bg-opacity-10 rounded-circle p-3">
                                                <i class="fas fa-clock text-danger"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="card-title mb-1">Overdue</h6>
                                            <h4 class="mb-0">@statistics.OverdueActions</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                                <i class="fas fa-check-circle text-success"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="card-title mb-1">Completed</h6>
                                            <h4 class="mb-0">@statistics.CompletedActions</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">Average Completion Time</h6>
                                    <h4 class="mb-0">@statistics.AverageCompletionDays.ToString("F1") days</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">On-Time Completion Rate</h6>
                                    <h4 class="mb-0">@statistics.OnTimeCompletionRate.ToString("P1")</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h6 class="card-title text-muted">Effectiveness Rate</h6>
                                    <h4 class="mb-0">@statistics.EffectivenessRate.ToString("P1")</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <!-- Filters -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <input type="text" class="form-control" placeholder="Search actions..." 
                                       @bind="searchTerm" @onkeypress="OnSearchKeyPress" />
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <select class="form-select" @bind="selectedStatus">
                                    <option value="">All Statuses</option>
                                    <option value="@CorrectiveActionStatus.Assigned">Assigned</option>
                                    <option value="@CorrectiveActionStatus.InProgress">In Progress</option>
                                    <option value="@CorrectiveActionStatus.CompletedPendingVerification">Completed</option>
                                    <option value="@CorrectiveActionStatus.VerifiedClosed">Verified</option>
                                    <option value="@CorrectiveActionStatus.Cancelled">Cancelled</option>
                                    <option value="@CorrectiveActionStatus.Ineffective">Ineffective</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Assigned User</label>
                                <select class="form-select" @bind="selectedAssignedUserId">
                                    <option value="">All Users</option>
                                    <!-- TODO: Load users dynamically -->
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Due Date</label>
                                <select class="form-select" @bind="dueDateFilter">
                                    <option value="">All Dates</option>
                                    <option value="overdue">Overdue</option>
                                    <option value="due-today">Due Today</option>
                                    <option value="due-this-week">Due This Week</option>
                                    <option value="due-next-week">Due Next Week</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Quick Filter</label>
                                <select class="form-select" @bind="quickFilter">
                                    <option value="">All Actions</option>
                                    <option value="my-actions">My Actions</option>
                                    <option value="overdue">Overdue Only</option>
                                    <option value="pending-verification">Pending Verification</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary w-100" @onclick="ApplyFilters">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions List -->
                @if (correctiveActions?.Items?.Any() == true)
                {
                    <div class="row">
                        @foreach (var action in correctiveActions.Items)
                        {
                            <div class="col-12 mb-3">
                                <div class="card border-0 shadow-sm @GetActionCardClass(action)">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0">
                                                        <a href="/corrective-actions/@action.Id" class="text-decoration-none">
                                                            Action #@action.Id.Substring(0, 8)
                                                        </a>
                                                    </h6>
                                                    <div class="d-flex gap-2">
                                                        <span class="badge @GetStatusBadgeClass(action.Status)">@action.Status</span>
                                                        @if (action.IsOverdue)
                                                        {
                                                            <span class="badge bg-danger">
                                                                <i class="fas fa-exclamation-triangle me-1"></i>Overdue
                                                            </span>
                                                        }
                                                    </div>
                                                </div>
                                                
                                                <p class="text-muted mb-2">@action.ActionDescription</p>
                                                
                                                <div class="mb-2">
                                                    <small class="text-muted">
                                                        <strong>Finding:</strong> 
                                                        <a href="/findings/@action.FindingId" class="text-decoration-none">
                                                            @(action.FindingCode ?? $"#{action.FindingId.Substring(0, 8)}")
                                                        </a>
                                                        - @action.FindingDescription.Substring(0, Math.Min(100, action.FindingDescription.Length))@(action.FindingDescription.Length > 100 ? "..." : "")
                                                    </small>
                                                </div>
                                                
                                                @if (!string.IsNullOrEmpty(action.AssignedUserFullName))
                                                {
                                                    <div class="mb-2">
                                                        <small class="text-muted">
                                                            <i class="fas fa-user me-1"></i>
                                                            <strong>Assigned to:</strong> @action.AssignedUserFullName
                                                        </small>
                                                    </div>
                                                }
                                            </div>
                                            <div class="col-md-4">
                                                <div class="text-end">
                                                    <div class="mb-2">
                                                        <small class="text-muted">
                                                            <strong>Due:</strong> 
                                                            <span class="@(action.IsOverdue ? "text-danger fw-bold" : "")">
                                                                @action.DueDate.ToString("MMM dd, yyyy")
                                                            </span>
                                                        </small>
                                                    </div>
                                                    <div class="mb-2">
                                                        <small class="text-muted">
                                                            <strong>Area:</strong> @action.AreaName
                                                        </small>
                                                    </div>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="/corrective-actions/@action.Id">
                                                                <i class="fas fa-eye me-2"></i>View Details
                                                            </a></li>
                                                            @if (CanUpdateStatus(action))
                                                            {
                                                                <li><a class="dropdown-item" href="#" @onclick="() => ShowStatusModal(action)">
                                                                    <i class="fas fa-edit me-2"></i>Update Status
                                                                </a></li>
                                                            }
                                                            @if (CanComplete(action))
                                                            {
                                                                <li><a class="dropdown-item" href="#" @onclick="() => ShowCompleteModal(action)">
                                                                    <i class="fas fa-check me-2"></i>Mark Complete
                                                                </a></li>
                                                            }
                                                            @if (CanVerify(action))
                                                            {
                                                                <li><a class="dropdown-item" href="#" @onclick="() => ShowVerifyModal(action)">
                                                                    <i class="fas fa-check-double me-2"></i>Verify
                                                                </a></li>
                                                            }
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item" href="/findings/@action.FindingId">
                                                                <i class="fas fa-exclamation-triangle me-2"></i>View Finding
                                                            </a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>

                    <!-- Pagination -->
                    @if (correctiveActions.TotalPages > 1)
                    {
                        <nav aria-label="Corrective actions pagination">
                            <ul class="pagination justify-content-center">
                                <li class="page-item @(correctiveActions.PageNumber <= 1 ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => GoToPage(correctiveActions.PageNumber - 1)" disabled="@(correctiveActions.PageNumber <= 1)">
                                        Previous
                                    </button>
                                </li>
                                
                                @for (int i = Math.Max(1, correctiveActions.PageNumber - 2); i <= Math.Min(correctiveActions.TotalPages, correctiveActions.PageNumber + 2); i++)
                                {
                                    var pageNumber = i;
                                    <li class="page-item @(correctiveActions.PageNumber == pageNumber ? "active" : "")">
                                        <button class="page-link" @onclick="() => GoToPage(pageNumber)">
                                            @pageNumber
                                        </button>
                                    </li>
                                }
                                
                                <li class="page-item @(correctiveActions.PageNumber >= correctiveActions.TotalPages ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => GoToPage(correctiveActions.PageNumber + 1)" disabled="@(correctiveActions.PageNumber >= correctiveActions.TotalPages)">
                                        Next
                                    </button>
                                </li>
                            </ul>
                        </nav>
                    }
                }
                else if (!isLoading)
                {
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No corrective actions found</h5>
                        <p class="text-muted">Try adjusting your search criteria or create a new corrective action.</p>
                    </div>
                }
            }
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private PagedResult<CorrectiveActionDto>? correctiveActions;
    private CorrectiveActionStatisticsModel? statistics;

    // Filter properties
    private string searchTerm = string.Empty;
    private string selectedStatus = string.Empty;
    private string selectedAssignedUserId = string.Empty;
    private string dueDateFilter = string.Empty;
    private string quickFilter = string.Empty;
    private int currentPage = 1;
    private const int pageSize = 20;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Load statistics
            statistics = await CorrectiveActionApiService.GetCorrectiveActionStatisticsAsync();

            // Apply filters
            var status = !string.IsNullOrEmpty(selectedStatus) ? Enum.Parse<CorrectiveActionStatus>(selectedStatus) : (CorrectiveActionStatus?)null;
            var assignedUserId = !string.IsNullOrEmpty(selectedAssignedUserId) ? selectedAssignedUserId : null;

            bool? isOverdue = null;
            DateTime? dueBefore = null;
            DateTime? dueAfter = null;

            // Apply due date filters
            switch (dueDateFilter)
            {
                case "overdue":
                    isOverdue = true;
                    break;
                case "due-today":
                    dueBefore = DateTime.Today.AddDays(1);
                    dueAfter = DateTime.Today;
                    break;
                case "due-this-week":
                    dueBefore = DateTime.Today.AddDays(7);
                    dueAfter = DateTime.Today;
                    break;
                case "due-next-week":
                    dueBefore = DateTime.Today.AddDays(14);
                    dueAfter = DateTime.Today.AddDays(7);
                    break;
            }

            // Apply quick filters
            switch (quickFilter)
            {
                case "overdue":
                    isOverdue = true;
                    break;
                case "pending-verification":
                    status = CorrectiveActionStatus.CompletedPendingVerification;
                    break;
                // TODO: Implement "my-actions" filter when user context is available
            }

            correctiveActions = await CorrectiveActionApiService.GetCorrectiveActionsAsync(
                searchTerm: !string.IsNullOrEmpty(searchTerm) ? searchTerm : null,
                status: status,
                assignedUserId: assignedUserId,
                isOverdue: isOverdue,
                dueBefore: dueBefore,
                dueAfter: dueAfter,
                pageNumber: currentPage,
                pageSize: pageSize);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", "Error loading corrective actions:", ex.Message);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private async Task ApplyFilters()
    {
        currentPage = 1;
        await LoadData();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await ApplyFilters();
        }
    }

    private async Task GoToPage(int pageNumber)
    {
        if (pageNumber >= 1 && pageNumber <= (correctiveActions?.TotalPages ?? 1))
        {
            currentPage = pageNumber;
            await LoadData();
        }
    }

    private void ShowCreateActionModal()
    {
        // TODO: Implement create action modal
        JSRuntime.InvokeVoidAsync("alert", "Create corrective action functionality will be implemented soon.");
    }

    private void ShowStatusModal(CorrectiveActionDto action)
    {
        // TODO: Implement status update modal
        JSRuntime.InvokeVoidAsync("alert", $"Status update for action {action.Id.Substring(0, 8)} will be implemented soon.");
    }

    private void ShowCompleteModal(CorrectiveActionDto action)
    {
        // TODO: Implement complete action modal
        JSRuntime.InvokeVoidAsync("alert", $"Complete action {action.Id.Substring(0, 8)} will be implemented soon.");
    }

    private void ShowVerifyModal(CorrectiveActionDto action)
    {
        // TODO: Implement verify action modal
        JSRuntime.InvokeVoidAsync("alert", $"Verify action {action.Id.Substring(0, 8)} will be implemented soon.");
    }

    private static string GetActionCardClass(CorrectiveActionDto action)
    {
        return action.IsOverdue ? "border-danger" : "";
    }

    private static string GetStatusBadgeClass(CorrectiveActionStatus status)
    {
        return status switch
        {
            CorrectiveActionStatus.Assigned => "bg-info",
            CorrectiveActionStatus.InProgress => "bg-warning",
            CorrectiveActionStatus.CompletedPendingVerification => "bg-primary",
            CorrectiveActionStatus.VerifiedClosed => "bg-success",
            CorrectiveActionStatus.Cancelled => "bg-secondary",
            CorrectiveActionStatus.Ineffective => "bg-danger",
            _ => "bg-light"
        };
    }

    private static bool CanUpdateStatus(CorrectiveActionDto action)
    {
        return action.Status != CorrectiveActionStatus.VerifiedClosed &&
               action.Status != CorrectiveActionStatus.Cancelled;
    }

    private static bool CanComplete(CorrectiveActionDto action)
    {
        return action.Status == CorrectiveActionStatus.InProgress;
    }

    private static bool CanVerify(CorrectiveActionDto action)
    {
        return action.Status == CorrectiveActionStatus.CompletedPendingVerification;
    }
}
