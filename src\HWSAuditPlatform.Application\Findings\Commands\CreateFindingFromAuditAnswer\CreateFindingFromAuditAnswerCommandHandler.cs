using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Findings.DTOs;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Application.Findings.Commands.CreateFindingFromAuditAnswer;

/// <summary>
/// Handler for automatically creating findings from audit answers
/// </summary>
public class CreateFindingFromAuditAnswerCommandHandler : BaseCommandHandler<CreateFindingFromAuditAnswerCommand, FindingDto?>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateFindingFromAuditAnswerCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<FindingDto?> Handle(CreateFindingFromAuditAnswerCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Get the audit answer with all related data
        var auditAnswer = await _context.AuditAnswers
            .Include(aa => aa.Audit)
                .ThenInclude(a => a.AuditTemplate)
            .Include(aa => aa.Audit)
                .ThenInclude(a => a.Factory)
            .Include(aa => aa.Audit)
                .ThenInclude(a => a.Area)
            .Include(aa => aa.Audit)
                .ThenInclude(a => a.SubArea)
            .Include(aa => aa.Question)
            .Include(aa => aa.FailureReasons)
            .FirstOrDefaultAsync(aa => aa.Id == request.AuditAnswerId, cancellationToken);

        if (auditAnswer == null)
        {
            throw new NotFoundException("AuditAnswer", request.AuditAnswerId);
        }

        // Check if a finding already exists for this audit answer
        var existingFinding = await _context.Findings
            .FirstOrDefaultAsync(f => f.AuditAnswerId == request.AuditAnswerId, cancellationToken);

        if (existingFinding != null)
        {
            // Return existing finding as DTO
            return await MapExistingFindingToDto(existingFinding, auditAnswer, cancellationToken);
        }

        // Determine if this audit answer indicates a failure that requires a finding
        if (!ShouldCreateFinding(auditAnswer, request.ForceCreation))
        {
            return null; // No finding needed
        }

        // Create the finding
        var finding = await CreateFindingFromAnswer(auditAnswer, currentUserId, cancellationToken);

        _context.Findings.Add(finding);
        await _context.SaveChangesAsync(cancellationToken);

        // Return the created finding as DTO
        return await MapToDto(finding, auditAnswer, cancellationToken);
    }

    private static bool ShouldCreateFinding(Domain.Entities.Audits.AuditAnswer auditAnswer, bool forceCreation)
    {
        if (forceCreation)
            return true;

        // Skip if not applicable
        if (auditAnswer.IsNotApplicable)
            return false;

        // Check for explicit severity level
        if (auditAnswer.SeverityLevel.HasValue)
            return true;

        // Check for failure reasons
        if (auditAnswer.FailureReasons.Any())
            return true;

        // Check for "No" answers on Yes/No questions
        if (auditAnswer.Question.QuestionType == QuestionType.YesNo && 
            auditAnswer.AnswerBoolean == false)
            return true;

        // Check for negative indicators in text answers
        if (!string.IsNullOrEmpty(auditAnswer.AnswerText))
        {
            var negativeIndicators = new[] { "fail", "failed", "non-compliant", "issue", "problem", "defect", "missing" };
            var answerLower = auditAnswer.AnswerText.ToLowerInvariant();
            if (negativeIndicators.Any(indicator => answerLower.Contains(indicator)))
                return true;
        }

        return false;
    }

    private async Task<Finding> CreateFindingFromAnswer(Domain.Entities.Audits.AuditAnswer auditAnswer, string currentUserId, CancellationToken cancellationToken)
    {
        // Generate finding code
        var findingCode = await GenerateFindingCode(auditAnswer.Audit, cancellationToken);

        // Build finding description from audit answer
        var findingDescription = BuildFindingDescription(auditAnswer);

        // Determine severity level
        var severityLevel = auditAnswer.SeverityLevel ?? DetermineSeverityLevel(auditAnswer);

        // Build root cause analysis from failure reasons
        var rootCauseAnalysis = BuildRootCauseAnalysis(auditAnswer);

        // Get default finding category if template has categorization enabled
        var findingCategoryId = await GetDefaultFindingCategoryId(auditAnswer.Audit.AuditTemplateId, cancellationToken);

        var finding = new Finding
        {
            Id = CuidGenerator.Generate(),
            AuditAnswerId = auditAnswer.Id,
            FindingCode = findingCode,
            FindingDescription = findingDescription,
            FindingSeverityLevel = severityLevel,
            RootCauseAnalysis = rootCauseAnalysis,
            ImmediateActionTaken = auditAnswer.Comments, // Use comments as immediate action if provided
            Status = FindingStatus.Open,
            DueDate = CalculateDueDate(severityLevel),
            FindingCategoryId = findingCategoryId
        };

        return finding;
    }

    private static string BuildFindingDescription(Domain.Entities.Audits.AuditAnswer auditAnswer)
    {
        var description = $"Finding identified in question: {auditAnswer.Question.QuestionText}";

        if (auditAnswer.Question.QuestionType == QuestionType.YesNo && auditAnswer.AnswerBoolean == false)
        {
            description += " (Answer: No)";
        }
        else if (!string.IsNullOrEmpty(auditAnswer.AnswerText))
        {
            description += $" (Answer: {auditAnswer.AnswerText})";
        }

        if (!string.IsNullOrEmpty(auditAnswer.Comments))
        {
            description += $"\n\nComments: {auditAnswer.Comments}";
        }

        return description;
    }

    private static string? BuildRootCauseAnalysis(Domain.Entities.Audits.AuditAnswer auditAnswer)
    {
        if (!auditAnswer.FailureReasons.Any())
            return null;

        var reasons = auditAnswer.FailureReasons
            .OrderBy(fr => fr.DisplayOrder)
            .Select(fr => fr.ReasonText)
            .ToList();

        return string.Join("\n", reasons);
    }

    private static SeverityLevel DetermineSeverityLevel(Domain.Entities.Audits.AuditAnswer auditAnswer)
    {
        // Default severity determination logic
        if (auditAnswer.Question.QuestionType == QuestionType.YesNo && auditAnswer.AnswerBoolean == false)
        {
            // Check for critical keywords in question text
            var questionLower = auditAnswer.Question.QuestionText.ToLowerInvariant();
            if (questionLower.Contains("safety") || questionLower.Contains("critical") || questionLower.Contains("emergency"))
                return SeverityLevel.Critical;
            
            if (questionLower.Contains("major") || questionLower.Contains("significant"))
                return SeverityLevel.Major;
            
            return SeverityLevel.Minor;
        }

        return SeverityLevel.Minor; // Default to minor for other question types
    }

    private static DateOnly CalculateDueDate(SeverityLevel severityLevel)
    {
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        
        return severityLevel switch
        {
            SeverityLevel.Critical => today.AddDays(1), // 1 day for critical
            SeverityLevel.Major => today.AddDays(7), // 1 week for major
            SeverityLevel.Minor => today.AddDays(30), // 1 month for minor
            _ => today.AddDays(30)
        };
    }

    private async Task<string> GenerateFindingCode(Domain.Entities.Audits.Audit audit, CancellationToken cancellationToken)
    {
        var year = DateTime.UtcNow.Year;
        var factoryCode = GenerateFactoryCode(audit.Factory.FactoryName);
        
        // Get the next sequence number for this factory and year
        var lastFinding = await _context.Findings
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
                    .ThenInclude(a => a.Factory)
            .Where(f => GenerateFactoryCode(f.AuditAnswer.Audit.Factory.FactoryName) == factoryCode &&
                       f.CreatedAt.Year == year)
            .OrderByDescending(f => f.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        var sequenceNumber = 1;
        if (lastFinding?.FindingCode != null)
        {
            // Extract sequence number from last finding code (format: FND-FACTORY-YYYY-NNN)
            var parts = lastFinding.FindingCode.Split('-');
            if (parts.Length >= 4 && int.TryParse(parts[3], out var lastSequence))
            {
                sequenceNumber = lastSequence + 1;
            }
        }

        return $"FND-{factoryCode}-{year}-{sequenceNumber:D3}";
    }

    private static string GenerateFactoryCode(string factoryName)
    {
        if (string.IsNullOrWhiteSpace(factoryName))
            return "UNK";

        // Take first 3 characters of each word, up to 6 characters total
        var words = factoryName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var code = string.Join("", words.Take(2).Select(w => w.Length >= 3 ? w.Substring(0, 3) : w)).ToUpper();

        return code.Length > 6 ? code.Substring(0, 6) : (code.Length < 3 ? code.PadRight(3, 'X') : code);
    }

    private async Task<int?> GetDefaultFindingCategoryId(int auditTemplateId, CancellationToken cancellationToken)
    {
        var template = await _context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == auditTemplateId, cancellationToken);

        if (template?.EnableFindingCategorization != true)
            return null;

        // Get the first active finding category for this template as default
        var defaultCategory = await _context.FindingCategories
            .Where(fc => fc.AuditTemplateId == auditTemplateId && fc.IsActive)
            .OrderBy(fc => fc.CategoryName)
            .FirstOrDefaultAsync(cancellationToken);

        return defaultCategory?.Id;
    }

    private async Task<FindingDto> MapExistingFindingToDto(Finding finding, Domain.Entities.Audits.AuditAnswer auditAnswer, CancellationToken cancellationToken)
    {
        // Load additional data if needed
        var findingWithData = await _context.Findings
            .Include(f => f.FindingCategory)
            .Include(f => f.ResponsibleUser)
            .FirstOrDefaultAsync(f => f.Id == finding.Id, cancellationToken);

        return await MapToDto(findingWithData ?? finding, auditAnswer, cancellationToken);
    }

    private async Task<FindingDto> MapToDto(Finding finding, Domain.Entities.Audits.AuditAnswer auditAnswer, CancellationToken cancellationToken)
    {
        var responsibleUser = finding.ResponsibleUserId != null
            ? await _context.Users.FirstOrDefaultAsync(u => u.AdObjectGuid == finding.ResponsibleUserId, cancellationToken)
            : null;

        // Get reported by user details
        var reportedByUser = await _context.Users.FirstOrDefaultAsync(u => u.AdObjectGuid == finding.ReportedByUserId, cancellationToken);

        return new FindingDto
        {
            Id = finding.Id,
            AuditAnswerId = finding.AuditAnswerId,
            FindingCode = finding.FindingCode,
            FindingDescription = finding.FindingDescription,
            FindingSeverityLevel = finding.FindingSeverityLevel,
            RootCauseAnalysis = finding.RootCauseAnalysis,
            ImmediateActionTaken = finding.ImmediateActionTaken,
            Status = finding.Status,
            ReportedByUserId = finding.ReportedByUserId,
            ReportedByUserName = reportedByUser?.Username,
            ReportedByUserFullName = reportedByUser?.FullName,
            DueDate = finding.DueDate,
            ResponsibleUserId = finding.ResponsibleUserId,
            ResponsibleUserName = responsibleUser?.Username,
            ResponsibleUserFullName = responsibleUser?.FullName,
            FindingCategoryId = finding.FindingCategoryId,
            FindingCategoryName = finding.FindingCategory?.CategoryName,
            AuditId = auditAnswer.AuditId,
            FactoryName = auditAnswer.Audit.Factory.FactoryName,
            AreaName = auditAnswer.Audit.Area.AreaName,
            SubAreaName = auditAnswer.Audit.SubArea?.SubAreaName,
            IsOverdue = finding.IsOverdue,
            IsOpen = finding.IsOpen,
            IsClosed = finding.IsClosed,
            CorrectiveActionCount = finding.CorrectiveActionCount,
            OpenCorrectiveActionCount = finding.OpenCorrectiveActionCount,
            CreatedAt = finding.CreatedAt,
            UpdatedAt = finding.UpdatedAt,
            CreatedByUserId = finding.CreatedByUserId,
            UpdatedByUserId = finding.UpdatedByUserId
        };
    }
}