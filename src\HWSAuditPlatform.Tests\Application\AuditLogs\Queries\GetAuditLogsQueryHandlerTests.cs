using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.AuditLogs.DTOs;
using HWSAuditPlatform.Application.AuditLogs.Queries.GetAuditLogs;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Workflow;
using HWSAuditPlatform.Infrastructure.Persistence;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Application.AuditLogs.Queries;

public class GetAuditLogsQueryHandlerTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly GetAuditLogsQueryHandler _handler;

    public GetAuditLogsQueryHandlerTests()
    {
        _context = TestDbContextFactory.CreateInMemoryContext();
        _handler = new GetAuditLogsQueryHandler(_context);

        SeedTestData();
    }

    private void SeedTestData()
    {
        // Create test users
        var user1 = User.Create(
            username: "user1",
            firstName: "First1",
            lastName: "Last1",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: "user1-guid-123456789012345678901234567890",
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        var user2 = User.Create(
            username: "user2",
            firstName: "First2",
            lastName: "Last2",
            email: "<EMAIL>",
            roleId: 2,
            factoryId: null,
            isActive: true,
            adObjectGuid: "user2-guid-123456789012345678901234567890",
            adDistinguishedName: null,
            createdByUserId: "system"
        );

        _context.Users.AddRange(user1, user2);

        // Create test audit logs with different dates to test date range filtering
        var auditLogs = new[]
        {
            new AuditLog
            {
                EventTimestamp = new DateTime(2023, 12, 30, 10, 0, 0, DateTimeKind.Utc), // Dec 30 - outside range
                ServerReceivedAt = new DateTime(2023, 12, 30, 10, 0, 0, DateTimeKind.Utc),
                UserId = user1.AdObjectGuid,
                EntityType = "User",
                EntityId = "entity1",
                ActionType = "Create",
                NewValues = "{\"Name\":\"Test1\"}",
                Details = "Created test entity 1",
                IPAddress = "*************",
                AppVersion = "1.0.0"
            },
            new AuditLog
            {
                EventTimestamp = new DateTime(2023, 12, 31, 15, 0, 0, DateTimeKind.Utc), // Dec 31 - inside range
                ServerReceivedAt = new DateTime(2023, 12, 31, 15, 0, 0, DateTimeKind.Utc),
                UserId = user2.AdObjectGuid,
                EntityType = "AuditTemplate",
                EntityId = "template1",
                ActionType = "Update",
                OldValues = "{\"Name\":\"Old Template\"}",
                NewValues = "{\"Name\":\"New Template\"}",
                Details = "Updated template name",
                IPAddress = "*************",
                AppVersion = "1.0.0"
            },
            new AuditLog
            {
                EventTimestamp = new DateTime(2024, 1, 2, 12, 0, 0, DateTimeKind.Utc), // Jan 2 - outside range
                ServerReceivedAt = new DateTime(2024, 1, 2, 12, 0, 0, DateTimeKind.Utc),
                UserId = user1.AdObjectGuid,
                EntityType = "User",
                EntityId = "entity2",
                ActionType = "Delete",
                OldValues = "{\"Name\":\"Deleted Entity\"}",
                Details = "Deleted test entity",
                IPAddress = "*************",
                AppVersion = "1.0.1"
            }
        };

        _context.AuditLogs.AddRange(auditLogs);
        _context.SaveChanges();
    }

    [Fact]
    public async Task Handle_WithNoFilters_ShouldReturnAllAuditLogs()
    {
        // Arrange
        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                PageNumber = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Equal(3, result.TotalCount);
        Assert.Equal(3, result.Items.Count);
        Assert.Equal(1, result.PageNumber);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(1, result.TotalPages);
    }

    [Fact]
    public async Task Handle_WithUserIdFilter_ShouldReturnFilteredResults()
    {
        // Arrange
        var userId = "user1-guid-123456789012345678901234567890";
        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                UserId = userId,
                PageNumber = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.TotalCount);
        Assert.Equal(2, result.Items.Count);
        Assert.All(result.Items, item => Assert.Equal(userId, item.UserId));
    }

    [Fact]
    public async Task Handle_WithEntityTypeFilter_ShouldReturnFilteredResults()
    {
        // Arrange
        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                EntityType = "User",
                PageNumber = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.TotalCount);
        Assert.Equal(2, result.Items.Count);
        Assert.All(result.Items, item => Assert.Equal("User", item.EntityType));
    }

    [Fact]
    public async Task Handle_WithActionTypeFilter_ShouldReturnFilteredResults()
    {
        // Arrange
        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                ActionType = "Create",
                PageNumber = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Equal(1, result.TotalCount);
        Assert.Equal(1, result.Items.Count);
        Assert.Equal("Create", result.Items.First().ActionType);
    }

    [Fact]
    public async Task Handle_WithDateRangeFilter_ShouldReturnFilteredResults()
    {
        // Arrange - Use fixed dates to test date range filtering
        // The query handler adds a full day to ToDate, so we filter for Dec 31 only
        var fromDate = new DateTime(2023, 12, 31, 0, 0, 0, DateTimeKind.Utc); // Dec 31 start
        var toDate = new DateTime(2023, 12, 31, 23, 59, 59, DateTimeKind.Utc); // Dec 31 end (becomes Jan 1 midnight)

        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                FromDate = fromDate,
                ToDate = toDate,
                PageNumber = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Equal(1, result.TotalCount);
        Assert.Equal(1, result.Items.Count);
        Assert.Equal("AuditTemplate", result.Items.First().EntityType);
    }

    [Fact]
    public async Task Handle_WithSearchTerm_ShouldReturnFilteredResults()
    {
        // Arrange
        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                SearchTerm = "template",
                PageNumber = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Equal(1, result.TotalCount);
        Assert.Equal(1, result.Items.Count);
        Assert.Contains("template", result.Items.First().Details.ToLower());
    }

    [Fact]
    public async Task Handle_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                PageNumber = 2,
                PageSize = 2,
                SortBy = "EventTimestamp",
                SortDirection = "asc"
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Equal(3, result.TotalCount);
        Assert.Equal(1, result.Items.Count); // Only 1 item on page 2 with page size 2
        Assert.Equal(2, result.PageNumber);
        Assert.Equal(2, result.PageSize);
        Assert.Equal(2, result.TotalPages);
    }

    [Fact]
    public async Task Handle_WithSortByUsername_ShouldReturnSortedResults()
    {
        // Arrange
        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                SortBy = "Username",
                SortDirection = "asc",
                PageNumber = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Equal(3, result.TotalCount);
        var usernames = result.Items.Select(x => x.Username).ToList();
        Assert.Equal(usernames.OrderBy(x => x).ToList(), usernames);
    }

    [Fact]
    public async Task Handle_WithUsernameFilter_ShouldReturnFilteredResults()
    {
        // Arrange
        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                Username = "user1",
                PageNumber = 1,
                PageSize = 10
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.TotalCount);
        Assert.Equal(2, result.Items.Count);
        Assert.All(result.Items, item => Assert.Equal("user1", item.Username));
    }

    [Fact]
    public async Task Handle_ShouldIncludeUserInformation()
    {
        // Arrange
        var query = new GetAuditLogsQuery
        {
            Filter = new AuditLogFilterDto
            {
                PageNumber = 1,
                PageSize = 1
            }
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var auditLog = result.Items.First();
        Assert.NotEmpty(auditLog.Username);
        Assert.NotEmpty(auditLog.UserFullName);
        Assert.Contains("First", auditLog.UserFullName);
        Assert.Contains("Last", auditLog.UserFullName);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
