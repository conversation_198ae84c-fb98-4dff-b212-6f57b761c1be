using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.CorrectiveActions.Commands.CreateCorrectiveAction;
using HWSAuditPlatform.Application.CorrectiveActions.Commands.UpdateCorrectiveActionStatus;
using HWSAuditPlatform.Application.CorrectiveActions.Queries.GetCorrectiveActions;
using HWSAuditPlatform.Application.CorrectiveActions.DTOs;
using HWSAuditPlatform.ApiService.Models;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for managing corrective actions
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/corrective-actions")]
[Tags("Corrective Actions")]
[Authorize]
public class CorrectiveActionsController : BaseController
{
    public CorrectiveActionsController(IMediator mediator, ILogger<CorrectiveActionsController> logger)
        : base(mediator, logger)
    {
    }

    /// <summary>
    /// Gets corrective actions with filtering and pagination
    /// </summary>
    /// <param name="findingId">Filter by finding ID</param>
    /// <param name="assignedToUserId">Filter by assigned to user ID</param>
    /// <param name="assignedByUserId">Filter by assigned by user ID</param>
    /// <param name="status">Filter by corrective action status</param>
    /// <param name="dueDateFrom">Filter by due date range - from</param>
    /// <param name="dueDateTo">Filter by due date range - to</param>
    /// <param name="completionDateFrom">Filter by completion date range - from</param>
    /// <param name="completionDateTo">Filter by completion date range - to</param>
    /// <param name="isOverdue">Show only overdue corrective actions</param>
    /// <param name="isCompleted">Show only completed corrective actions</param>
    /// <param name="isInProgress">Show only in-progress corrective actions</param>
    /// <param name="searchTerm">Search term for action description</param>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction (asc/desc)</param>
    /// <returns>Paginated list of corrective actions</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<CorrectiveActionSummaryDto>>), 200)]
    public async Task<ActionResult<PagedResult<CorrectiveActionSummaryDto>>> GetCorrectiveActions(
        [FromQuery] string? findingId = null,
        [FromQuery] string? assignedToUserId = null,
        [FromQuery] string? assignedByUserId = null,
        [FromQuery] CorrectiveActionStatus? status = null,
        [FromQuery] DateOnly? dueDateFrom = null,
        [FromQuery] DateOnly? dueDateTo = null,
        [FromQuery] DateOnly? completionDateFrom = null,
        [FromQuery] DateOnly? completionDateTo = null,
        [FromQuery] bool? isOverdue = null,
        [FromQuery] bool? isCompleted = null,
        [FromQuery] bool? isInProgress = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string sortBy = "DueDate",
        [FromQuery] string sortDirection = "asc")
    {
        var query = new GetCorrectiveActionsQuery
        {
            FindingId = findingId,
            AssignedToUserId = assignedToUserId,
            AssignedByUserId = assignedByUserId,
            Status = status,
            DueDateFrom = dueDateFrom,
            DueDateTo = dueDateTo,
            CompletionDateFrom = completionDateFrom,
            CompletionDateTo = completionDateTo,
            IsOverdue = isOverdue,
            IsCompleted = isCompleted,
            IsInProgress = isInProgress,
            SearchTerm = searchTerm,
            PageNumber = pageNumber,
            PageSize = pageSize,
            SortBy = sortBy,
            SortDirection = sortDirection
        };

        var result = await Mediator.Send(query);
        return Success(result, "Corrective actions retrieved successfully");
    }

    /// <summary>
    /// Creates a new corrective action for a finding
    /// </summary>
    /// <param name="command">Corrective action creation data</param>
    /// <returns>Created corrective action</returns>
    [HttpPost]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(ApiResponse<CorrectiveActionDto>), 201)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<CorrectiveActionDto>> CreateCorrectiveAction(
        [FromBody] CreateCorrectiveActionCommand command)
    {
        var result = await Mediator.Send(command);
        return Created(nameof(GetCorrectiveActions), new { id = result.Id }, result, 
            "Corrective action created successfully");
    }

    /// <summary>
    /// Updates the status of a corrective action
    /// </summary>
    /// <param name="id">Corrective action ID</param>
    /// <param name="request">Status update request</param>
    /// <returns>Success confirmation</returns>
    [HttpPut("{id}/status")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(ApiResponse<object>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult> UpdateCorrectiveActionStatus(
        string id, 
        [FromBody] UpdateCorrectiveActionStatusRequest request)
    {
        var command = new UpdateCorrectiveActionStatusCommand
        {
            CorrectiveActionId = id,
            Status = request.Status,
            StatusChangeNotes = request.StatusChangeNotes,
            CompletionDate = request.CompletionDate,
            VerificationDate = request.VerificationDate
        };

        var success = await Mediator.Send(command);

        if (!success)
        {
            return BadRequest(ApiErrorResponse.BadRequest("Failed to update corrective action status."));
        }

        return Success("Corrective action status updated successfully");
    }

    /// <summary>
    /// Gets corrective actions for a specific finding
    /// </summary>
    /// <param name="findingId">Finding ID</param>
    /// <returns>List of corrective actions for the finding</returns>
    [HttpGet("finding/{findingId}")]
    [ProducesResponseType(typeof(ApiResponse<List<CorrectiveActionSummaryDto>>), 200)]
    public async Task<ActionResult<List<CorrectiveActionSummaryDto>>> GetFindingCorrectiveActions(string findingId)
    {
        var query = new GetCorrectiveActionsQuery
        {
            FindingId = findingId,
            PageSize = int.MaxValue // Get all corrective actions for the finding
        };

        var result = await Mediator.Send(query);
        return Success(result.Items.ToList(), "Finding corrective actions retrieved successfully");
    }

    /// <summary>
    /// Gets corrective actions assigned to a specific user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="includeCompleted">Whether to include completed actions</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated list of user's corrective actions</returns>
    [HttpGet("user/{userId}")]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<CorrectiveActionSummaryDto>>), 200)]
    public async Task<ActionResult<PagedResult<CorrectiveActionSummaryDto>>> GetUserCorrectiveActions(
        string userId,
        [FromQuery] bool includeCompleted = false,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20)
    {
        var query = new GetCorrectiveActionsQuery
        {
            AssignedToUserId = userId,
            IsCompleted = includeCompleted ? null : false,
            PageNumber = pageNumber,
            PageSize = pageSize,
            SortBy = "DueDate",
            SortDirection = "asc"
        };

        var result = await Mediator.Send(query);
        return Success(result, "User corrective actions retrieved successfully");
    }

    /// <summary>
    /// Gets overdue corrective actions
    /// </summary>
    /// <param name="assignedToUserId">Optional user filter</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated list of overdue corrective actions</returns>
    [HttpGet("overdue")]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<CorrectiveActionSummaryDto>>), 200)]
    public async Task<ActionResult<PagedResult<CorrectiveActionSummaryDto>>> GetOverdueCorrectiveActions(
        [FromQuery] string? assignedToUserId = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20)
    {
        var query = new GetCorrectiveActionsQuery
        {
            AssignedToUserId = assignedToUserId,
            IsOverdue = true,
            PageNumber = pageNumber,
            PageSize = pageSize,
            SortBy = "DueDate",
            SortDirection = "asc"
        };

        var result = await Mediator.Send(query);
        return Success(result, "Overdue corrective actions retrieved successfully");
    }
}

/// <summary>
/// Request model for updating corrective action status
/// </summary>
public class UpdateCorrectiveActionStatusRequest
{
    /// <summary>
    /// The new status for the corrective action
    /// </summary>
    public CorrectiveActionStatus Status { get; set; }

    /// <summary>
    /// Optional notes about the status change
    /// </summary>
    public string? StatusChangeNotes { get; set; }

    /// <summary>
    /// Completion date (required when marking as completed)
    /// </summary>
    public DateOnly? CompletionDate { get; set; }

    /// <summary>
    /// Verification date (required when marking as verified)
    /// </summary>
    public DateOnly? VerificationDate { get; set; }
}
